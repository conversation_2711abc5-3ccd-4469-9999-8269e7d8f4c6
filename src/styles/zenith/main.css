@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
body.zenith {
    font-family: 'Inter', sans-serif !important;
    .zenith-header {
        color: #ffffff;
        background-color: #007F00;
        padding: 10px 0;
        text-align: center;
        margin-bottom: 10px;
        font-size: 50px;
        font-weight: 300;
    }

    .footer {
        /* background-color: #007F00; */
        /* padding: 20px 0; */
        text-align: center;
        margin-top: 10px;
    }

    .zenith-footer a {
        color: #ffffff;
        font-weight: 300;
        font-size: 16px;
        text-decoration: none;
    }
}

/* Force left alignment for questionnaire components */
.questionnaire-container,
.questionnaire-container *,
.questionnaire-container .MuiFormControl-root,
.questionnaire-container .MuiFormLabel-root,
.questionnaire-container .MuiTypography-root,
.questionnaire-container .MuiFormControlLabel-root,
.questionnaire-container .MuiFormControlLabel-label,
.questionnaire-container .MuiFormGroup-root {
    text-align: left !important;
    text-align-last: left !important;
}

.questionnaire-container .MuiFormControlLabel-root {
    align-items: flex-start !important;
    justify-content: flex-start !important;
}

.questionnaire-container .MuiFormControlLabel-label {
    text-align: left !important;
    display: block !important;
    width: 100% !important;
}

.questionnaire-container .MuiFormLabel-root {
    text-align: left !important;
    display: block !important;
    width: 100% !important;
}

.questionnaire-container .MuiTypography-root {
    text-align: left !important;
    width: 100% !important;
}