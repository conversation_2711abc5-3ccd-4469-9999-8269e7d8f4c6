import { createTheme, responsiveFontSizes } from "@mui/material/styles";
import { red } from "@mui/material/colors";

// A custom theme for this app
const zenithTheme = createTheme({
	cssVariables: true,
	palette: {
		primary: {
			main: "#007F00",
		},
		secondary: {
			main: "#E0E0E0",
		},
		error: {
			main: red.A400,
		},
		success: {
			main: "#217F00",
		},
	},
	typography: {
		fontFamily: ["Inter", "Roboto"].join(","),
	},
});

export default responsiveFontSizes(zenithTheme);
