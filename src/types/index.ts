import moment from "moment";

export type RegistrationData = {
	firstname: string;
	lastname: string;
	fullName?: string;
	email: string;
	password_confirm: string;
	password: string;
	phone: string;
};

export type AuthUser = {
	firstname: string;
	lastname: string;
	fullName?: string;
	email: string;
	password: string;
	phone: string;
	phoneverified: boolean | undefined;
	laststep: string;
	status: string;
	uploaded_documents: {
		question_key: string;
		file_url: string | null;
	}[];
};

export type Questionnaire = {
	dob: moment.Moment | null;
	condition: string;
	first_medication: string;
	second_medication: string;
	children: string;
	disorder: string;
	diseases: string;
	alternative_medecine: string;
	addiction: string;
	treatment: string;
	trial: string;
	gender: string;
	medicare_number: string;
	general_practitioner: string;
	weight: string;
	height: string;
	blood_pressure: string;
	heart_rate: string;
	gp_details: {
		gp_name: string;
		gp_practice_name: string;
		gp_contact: string;
	};
	gpConsent: boolean;
	medicare_index_number: string;
};

export type TreatmentPlan = {
	consultingDoctor: string;
	treatmentPlanStartDate: string;
	treatmentPlanEndDate: string;
	thcContent: string;
	totalAllowance: {
		thc22: string;
		thc29: string;
	};
	totalAllowanceUsed: {
		thc22: string;
		thc29: string;
	};
	repeatAllowance: {
		thc22: string;
		thc29: string;
	};
	numberOfRepeats: number;
	repeatsRemaining: {
		thc22: number;
		thc29: number;
	};
	nextRepeatDate: {
		thc22: string;
		thc29: string;
	};
	supplyRemainingForRepeat: {
		thc22: string;
		thc29: string;
	};
};

export type TreatmentPlanResponse = {
	success: boolean;
	email: string;
	contactId: string;
	fullName: string;
	treatmentPlan: TreatmentPlan;
	additionalInfo: {
		memberStatus: string;
		memberID: string;
		doctorNotes: string | null;
		mentalHealthSupportingDocumentation: string;
		dispensingInterval: string;
		maximumDosesPerDay: {
			thc22: string | null;
			thc29: string | null;
		};
		pouchCount: {
			thc22: string | null;
			thc29: string | null;
		};
		lastModified: string;
		createdTime: string;
	};
};

// 22% THC Addition Questionnaire Types
export interface Add22ThcFormData {
	// Question 1: Reason for requesting 22% THC
	reasonSideEffects: boolean;
	reasonGentlerEffect: boolean;
	reasonDifferentStrain: boolean;
	reasonTolerance: boolean;
	reasonOther: boolean;
	reasonOtherText: string;

	// Question 2: Current response to 29% THC
	symptomImprovement: string; // 1-10 scale
	sideEffectsNone: boolean;
	sideEffectsMild: boolean;
	sideEffectsModerate: boolean;
	sideEffectsStrong: boolean;
	sideEffectsDescription: string;

	// Question 3: Health changes
	healthChanges: string; // 'no-changes' | 'yes'
	healthChangesDescription: string;

	// Question 4: Expectations and preferences
	expectations: string; // text field
	concerns: string; // text field
	usagePlan: string; // radio buttons

	// Question 5: Consent
	consent: string; // 'yes' | 'no'
}

export interface Add22ThcScoringState {
	totalScore: number;
	maxScore: number;
	isEligible: boolean;
	questionScores: Record<string, number>;
}

export interface Add22ThcQuestionnaireStatus {
	completed: boolean;
	score: number;
	isEligible: boolean;
	status: string; // 'submitted' | 'under_review' | 'approved' | 'rejected'
}

export interface Add22ThcSubmissionData {
	questionsAndAnswers: Array<{
		questionKey: string;
		questionText: string;
		answerValue: string | boolean;
		answerText: string;
		score: number;
	}>;
	totalScore: number;
	isEligible: boolean;
}

// Dynamic Questionnaire Configuration Types (Updated to match actual API)
export interface QuestionnaireApiResponse {
	success: boolean;
	message: string;
	data: QuestionnaireConfigResponse;
	error: any;
	code: number;
}

export interface QuestionnaireConfigResponse {
	id: string;
	name: string;
	maxScore: number;
	threshold: number;
	version: string;
	sectionsCount: number;
	questions: QuestionConfig[];
	sections: SectionConfig[];
	lastModified: string;
}

export interface SectionConfig {
	id: string;
	title: string;
	description: string;
	order_index: number;
	validation_rules: {
		requireAllQuestions: boolean;
	};
	is_active: boolean;
}

export interface QuestionConfig {
	key: string;
	text: string;
	type: 'radio' | 'checkbox' | 'slider' | 'text' | 'dropdown';
	order: number;
	sectionId: string;
	answerOptions: AnswerOption[];
	contributesToScore: boolean;
	textFieldConfig?: {
		purpose?: string;
		required?: boolean;
		maxLength?: number;
		placeholder?: string;
	};
	sliderConfig?: {
		max: number;
		min: number;
		scoreMapping: Record<string, number>;
	};
}

export interface AnswerOption {
	label: string;
	score: number;
	value: string;
}

// Legacy interfaces for backward compatibility
export interface StepConfig {
	stepNumber: number;
	title: string;
	questions: QuestionConfig[];
	section?: SectionConfig;
}

export interface OptionConfig {
	value: string;
	label: string;
	score?: number;
}

export interface SliderConfig {
	min: number;
	max: number;
	step: number;
	marks: boolean;
	valueLabelDisplay: 'on' | 'off' | 'auto';
}

export interface TextConfig {
	multiline: boolean;
	rows?: number;
	maxLength?: number;
	placeholder?: string;
}

export interface ConditionalConfig {
	dependsOn: string;
	showWhen: string[];
}

export interface SpecialLogicConfig {
	questionKey: string;
	logicType: 'condition' | 'sideEffect';
	description: string;
}
