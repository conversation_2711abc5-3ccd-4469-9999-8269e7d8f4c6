import React from "react";
import { Box, useTheme, useMediaQuery, CssBaseline, Toolbar } from "@mui/material";
import FormRegister from "./components/zenith/forms/FormRegister";
import FormQuestionnaire from "./components/zenith/forms/FormQuestionaire";
import FormDischarge from "./components/zenith/forms/FormDischarge";
import FormConsult from "./components/zenith/forms/FormConsult";
import FormConsent from "./components/zenith/forms/FormConsent";
import { useCaptureUTM } from "./hooks/useCaptureUTM";

import { default as HarvestHome } from "./components/harvest/pages/Home";
import { default as HarvestDischarge } from "./components/harvestdischarge/pages/Home";
import { default as HarvestDischargeFunnel } from "./components/harvestdischarge/pages/Funnel";
import { default as HarvestThankyou } from "./components/harvest/pages/Thankyou";
import { default as HarvestThankyouOther } from "./components/harvest/pages/Thankyouother";
import { default as HarvestThankyouA } from "./components/harvestdischarge/pages/Thankyou_a";
import { default as HarvestThankyouB } from "./components/harvestdischarge/pages/Thankyou_b";
import { default as HarvestThankyouC } from "./components/harvestdischarge/pages/Thankyou_c";
import { default as HarvestThankyouD } from "./components/harvestdischarge/pages/Thankyou_d";
import Review from "./components/zenith/pages/GoogleReview";

import { Navigate, ReactLocation, Route, Router, MakeGenerics } from "@tanstack/react-location";
import NotFound from "./utils/not-found";
import { AuthProvider } from "./hooks/auth-provider";
import HealthCheck from "./components/zenith/forms/healthCheck";
import Shell from "./shell";
import PhoneVerification from "./components/zenith/forms/PhoneVerification";
import Confirmation from "./components/zenith/pages/Confirmation";
import ConfirmationOnCall from "./components/zenith/pages/ConfirmationOnCall";
import Login from "./components/zenith/pages/Login";
import { FlowController } from "./hooks/flow-controller";
import ForgotPassword from "./components/zenith/pages/ForgotPassword";
import ResetPassword from "./components/zenith/pages/ResetPassword";
import HarvestIntroduce from "./components/zenith/pages/HarvestIntroduce";
import Schedule from "./components/zenith/pages/Schedule";
import Profile from "./components/zenith/pages/Profile";
import Chat from "./components/zenith/pages/Chat";
import LandingPage from "./components/zenith/pages/Home";
import { preloadCriticalImages } from "./utils/imagePreloader";
import TermsConditions from "./components/zenith/pages/TermsConditions";
import PendingReview from "./components/zenith/pages/PendingReview";
import NotApproved from "./components/zenith/pages/NotApproved";
import ThcIncrease from "./components/zenith/pages/ThcIncrease";
import ThcIncreaseDynamic from "./components/zenith/pages/ThcIncreaseDynamic";
import ExtendTP from "./components/zenith/pages/ExtendTP";
import ExtendTPDynamic from "./components/zenith/pages/ExtendTPDynamic";
import Add22Thc from "./components/zenith/pages/Add22Thc";
import Add22ThcDynamic from "./components/zenith/pages/Add22ThcDynamic";
import QuantityIncrease from "./components/zenith/pages/QuantityIncrease";
import QuantityIncreaseDynamic from "./components/zenith/pages/QuantityIncreaseDynamic";
import ThankYouMessage from "./components/zenith/pages/ThankYouMessage";
import Bookings from "./components/zenith/pages/Bookings";
import FormBloodPressure from "./components/zenith/forms/FormBloodPressure";
import FormHeartRate from "./components/zenith/forms/FormHeartRate";
import Approved from "./components/zenith/pages/Approved";

// Define a generics type to ensure TypeScript knows token is a string
type LocationGenerics = MakeGenerics<{
	Search: {
		token?: string; // Make token optional since other params might exist
		return_to?: string; // Add return_to parameter
		[key: string]: string | undefined; // Allow any other string parameters
	};
}>;

// Create a new ReactLocation instance with custom parsing
const location = new ReactLocation<LocationGenerics>({
	parseSearch: (searchStr) => {
		const params = new URLSearchParams(searchStr);
		const result: { [key: string]: string | undefined } = {};

		// Iterate through all parameters and preserve them
		for (const [key, value] of params.entries()) {
			if (key === "token") {
				// Clean the token by removing any quotes
				result[key] = value ? value.replace(/['"]/g, "") : "";
			} else {
				// Preserve all other parameters as-is
				result[key] = value;
			}
		}

		return result;
	},
	stringifySearch: (search) => {
		const params = new URLSearchParams();

		// Add all search parameters to the URL
		Object.entries(search).forEach(([key, value]) => {
			if (value !== undefined && value !== null && value !== "") {
				params.set(key, value);
			}
		});

		return params.toString();
	},
});

const routes: Route<LocationGenerics>[] = [
	{
		path: "/",
		element: <Navigate to="/patient/register" />,
	},
	{
		path: "/patient/register",
		element: <FormRegister />,
	},
	{
		path: "/patient/phone-verification",
		element: <PhoneVerification />,
	},
	{
		path: "/patient/login",
		element: <Login />,
	},
	{
		path: "/patient/forgot-password",
		element: <ForgotPassword />,
	},
	{
		path: "/patient/reset-password",
		element: <ResetPassword />,
	},
	{
		path: "/patient/home",
		element: <LandingPage />,
	},
	{
		path: "/patient/home/<USER>",
		element: <LandingPage />,
	},
	{
		path: "/patient/confirm",
		element: <Confirmation />,
	},
	{
		path: "/patient/confirm-on-call-booking",
		element: <ConfirmationOnCall />,
	},
	{
		path: "/patient/questionnaire",
		element: <FormQuestionnaire />,
	},
	{
		path: "/patient/discharge-letter",
		element: <FormDischarge />,
	},
	{
		path: "/patient/consult-fee",
		element: <FormConsult />,
	},
	{
		path: "/patient/terms-conditions",
		element: <TermsConditions />,
	},
	{
		path: "/patient/consent",
		element: <FormConsent />,
	},
	{
		path: "/patient/review",
		element: <Review />,
	},
	{
		path: "/patient/introduce-harvest",
		element: <HarvestIntroduce />,
	},
	{
		path: "/patient/health-survey",
		element: <HealthCheck />,
	},
	{
		path: "/patient/profile",
		element: <Profile />,
	},
	{
		path: "/patient/profile/:contactId",
		element: <Profile />,
	},
	{
		path: "/patient/chat",
		element: <Chat />,
	},
	{
		path: "/patient/bookings",
		element: <Bookings />,
	},
	{
		path: "/patient/schedule",
		element: <Schedule />,
	},
	{
		path: "/schedule",
		element: <Schedule />,
	},
	{
		path: "/patient/pending-review",
		element: <PendingReview />,
	},
	{
		path: "/patient/not-approved",
		element: <NotApproved />,
	},
	{
		path: "/patient/increase",
		element: <ThcIncrease />,
	},
	{
		path: "/patient/increase-dynamic",
		element: <ThcIncreaseDynamic />,
	},
	{
		path: "/patient/extend",
		element: <ExtendTP />,
	},
	{
		path: "/patient/extend-dynamic",
		element: <ExtendTPDynamic />,
	},
	{
		path: "/patient/add-22-thc",
		element: <Add22Thc />,
	},
	{
		path: "/patient/add-22-thc-dynamic",
		element: <Add22ThcDynamic />,
	},
	{
		path: "/patient/quantity-increase",
		element: <QuantityIncrease />,
	},
	{
		path: "/patient/quantity-increase-dynamic",
		element: <QuantityIncreaseDynamic />,
	},
	{
		path: "/patient/thankyou",
		element: <ThankYouMessage />,
	},
	{
		path: "/patient/upload-blood-pressure",
		element: <FormBloodPressure />,
	},
	{
		path: "/patient/upload-heart-rate",
		element: <FormHeartRate />,
	},
	{
		path: "/patient/approved",
		element: <Approved />,
	},
	/*{
    path: "/harvest/thankyou",
    element: <HarvestThankyou />,
  },
  {
    path: "/harvest/thankyou_b",
    element: <HarvestThankyouOther />,
  },
  {
    path: "/harvest",
    element: <HarvestHome />,
  },
  {
    path: "/harvestdischarge/quiz",
    element: <HarvestDischargeFunnel />,
  },
  {
    path: "/harvestdischarge/thankyou_a",
    element: <HarvestThankyouA />,
  },
  {
    path: "/harvestdischarge/thankyou_b",
    element: <HarvestThankyouB />,
  },
  {
    path: "/harvestdischarge/thankyou_c",
    element: <HarvestThankyouC />,
  },
  {
    path: "/harvestdischarge/thankyou_d",
    element: <HarvestThankyouD />,
  },
  {
    path: "/harvestdischarge",
    element: <HarvestDischarge />,
  },*/
	{
		path: "*",
		element: <NotFound />,
	},
];

const AppLayout: React.FC = () => {
	useCaptureUTM();
	return (
		<>
			{/* <AuthProvider> */}
			<FlowController>
				<Shell />
			</FlowController>
			{/* </AuthProvider> */}
		</>
	);
};

function App() {
	// Preload critical images when the app starts
	React.useEffect(() => {
		preloadCriticalImages();
	}, []);

	return (
		<>
			<CssBaseline />
			<Router location={location} routes={routes}>
				<AppLayout />
			</Router>
		</>
	);
}

export default App;