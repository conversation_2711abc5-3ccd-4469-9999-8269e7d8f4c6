import {
	Button,
	Dialog,
	DialogContent,
	DialogTitle,
	Divider,
	Fab,
	FormControl,
	FormControlLabel,
	FormHelperText,
	Radio,
	RadioGroup,
	TextField,
	Typography,
	useMediaQuery,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import zenithTheme from "../../../styles/zenith/theme";
import styles from "../../../css/confirmation.module.css";
import moment from "moment";

export default function ConfirmationDialog({
	openDialog,
	consultFromDate,
	consultToDate,
	patientName,
	consultDate,
}: any) {
	const isDesktop = useMediaQuery(zenithTheme.breakpoints.up("sm"));
	return (
		<Dialog
			open={openDialog}
			fullScreen={!isDesktop}
			maxWidth={isDesktop ? "xs" : false}
			disableEscapeKeyDown
			onClose={() => {}}
		>
			{!isDesktop && (
				<DialogTitle
					sx={{
						backgroundColor: "green",
						p: 2,
						display: "flex",
						justifyContent: "center",
						alignItems: "center",
					}}
				>
					<img src="/zenith/zenith-logo.svg" alt="Zenith Clinics Logo" />
				</DialogTitle>
			)}
			<DialogContent>
				<Grid
					container
					direction={"column"}
					sx={{ width: "100%", pt: 2 }}
					justifyContent={"center"}
					alignItems={"center"}
					textAlign={"center"}
				>
					<Typography variant="h3" fontWeight={"bold"}>
						Thank You for <span style={{ color: "green" }}>Confirming Your Consultation</span>
					</Typography>
					<img className={styles.confirmImg} src="/zenith/confirm.png" />
					<Grid
						sx={{ width: "100%", fontSize: "14px", margin: "20px 0px", gap: "14px" }}
						textAlign={"start"}
						container
						direction={"column"}
						alignItems={"start"}
						size={12}
					>
						<Typography variant="body1">
							Dear {patientName}, Thank you for booking your appointment on{" "}
							{moment(consultDate).format("MMMM Do YYYY")} between {moment(consultDate).format("h:mm A")}{" "}
							and {moment(consultToDate).format("h:mm A")}.
						</Typography>
						<Typography variant="body1">
							We appreciate the opportunity to assist you on your treatment journey.
						</Typography>
						<Typography variant="body1">
							At your scheduled time, you will receive an{" "}
							<span style={{ fontWeight: "bold" }}>SMS with a link to join our consultation.</span> Please
							ensure you are in a quiet, private space for our appointment.
						</Typography>
						<Typography variant="body1">We look forward to speaking with you soon.</Typography>
						<Typography variant="body1">
							<span style={{ fontWeight: "bold" }}>Best regards</span>,
							<br />
							Zenith Clinics Team
						</Typography>
						<Button
							type="submit"
							fullWidth
							variant="contained"
							sx={{ mt: 2 }}
							onClick={() => {
								window.location.href = `/patient/review`;
								// window.location.href = import.meta.env.VITE_MAIN_ZENITH_URL ;
							}}
						>
							Continue
						</Button>
					</Grid>
				</Grid>
			</DialogContent>
		</Dialog>
	);
}
