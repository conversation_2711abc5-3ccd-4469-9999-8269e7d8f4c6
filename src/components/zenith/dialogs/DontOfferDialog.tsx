import { But<PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogTitle, ThemeProvider, Typography, useMediaQuery } from "@mui/material";
import Grid from "@mui/material/Grid2";
import zenithTheme from "../../../styles/zenith/theme";
import styles from "../../../css/confirmation.module.css";

export default function DontOfferDialog({ openDialog, setOpenDialog, onClickAction }: any) {
	const isDesktop = useMediaQuery(zenithTheme.breakpoints.up("sm"));
	const liStyle = {
		padding: "5px 20px",
		margin: "10px 0px",
		fontSize: "16px",
		color: "white",
		backgroundColor: "#FF7171",
		borderRadius: "6px",
	};
	return (
		<ThemeProvider theme={zenithTheme}>
			<Dialog
				open={openDialog}
				fullWidth={true}
				fullScreen={!isDesktop}
				maxWidth={isDesktop ? "xs" : false}
				disableEscapeKeyDown
			>
				{!isDesktop && (
					<DialogTitle
						sx={{
							backgroundColor: "green",
							p: 2,
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
						}}
					>
						<img src="/zenith/zenith-logo.svg" alt="Zenith Clinics Logo" />
					</DialogTitle>
				)}
				<DialogContent sx={{ paddingX: "30px", maxHeight: "550px", overflow: "hidden" }}>
					<Grid
						container
						direction={"column"}
						sx={{ width: "100%" }}
						justifyContent={"center"}
						alignItems={"center"}
						textAlign={"center"}
						mt="20px"
					>
						<Typography
							sx={{
								fontSize: "35px",
								fontWeight: "700",
								lineHeight: "100%",
							}}
						>
							<span style={{ color: "green" }}>What We </span> <br />
							Don't Offer
						</Typography>
					</Grid>
					<Grid sx={{ marginTop: "20px" }}>
						<ul style={{ listStylePosition: "inside", padding: 0 }}>
							<li style={liStyle}>We do not offer oils</li>
							<li style={liStyle}>We do not offer edibles</li>
							<li style={liStyle}>We do not provide e-scripts</li>
						</ul>
					</Grid>
					<Typography
						sx={{
							fontSize: "16px",
							fontWeight: "400",
						}}
					>
						At Zenith Clinics, we focus on what we do best : Providing eligible patients with{" "}
						<b>natural alternatives</b>, prescribed and supplied to you by your Doctor.
					</Typography>
					<Grid
						sx={{ width: "100%" }}
						textAlign={"center"}
						container
						direction={"row"}
						alignItems={"center"}
						justifyContent={"space-evenly"}
						spacing={2}
					>
						<Grid size={5}>
							<Button
								type="submit"
								variant="contained"
								sx={{ mt: 2, textTransform: "none" }}
								onClick={onClickAction}
								fullWidth
							>
								I Understand
							</Button>
						</Grid>

						<Grid size={5}>
							<Button
								type="submit"
								variant="outlined"
								sx={{ mt: 2, textTransform: "none" }}
								onClick={() => setOpenDialog(false)}
								fullWidth
							>
								Go Back
							</Button>
						</Grid>
					</Grid>
				</DialogContent>
			</Dialog>
		</ThemeProvider>
	);
}
