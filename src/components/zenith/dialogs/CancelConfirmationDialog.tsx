import {
	<PERSON>ton,
	<PERSON>alog,
	DialogContent,
	DialogTitle,
	Divider,
	Fab,
	FormControl,
	FormControlLabel,
	FormHelperText,
	Radio,
	RadioGroup,
	TextField,
	Typography,
	useMediaQuery,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import zenithTheme from "../../../styles/zenith/theme";
import styles from "../../../css/confirmation.module.css";
import LoadingScreen from "../../../utils/loading-screen";
import { useState } from "react";
import { enqueueSnackbar } from "notistack";
import axios from "axios";
import axiosInstance from "../../../services/axios";
import moment from "moment";

export default function CancelConfirmationDialog({ openDialog, setOpenDialog, leadId, consultDate }: any) {
	const [isLoading, setIsLoading] = useState(false);
	const isDesktop = useMediaQuery(zenithTheme.breakpoints.up("sm"));
	async function handleSubmitConfirm(e: any) {
		try {
			e.preventDefault();
			setIsLoading(true);
			await updateZohoLeadCancelBooking(leadId);
			enqueueSnackbar("You have cancelled your appointment with the doctor", {
				variant: "success",
			});
			window.location.href = import.meta.env.VITE_MAIN_ZENITH_URL;
			setIsLoading(false);
		} catch (e: any) {
			enqueueSnackbar("An error occured", {
				variant: "error",
			});
		} finally {
			setIsLoading(false);
		}
	}
	const updateZohoLeadCancelBooking = async (leadId: string) => {
		await axiosInstance.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/updateZohoLeadCancelBooking`, {
			leadId: leadId,
		});
	};
	return (
		<>
			{isLoading && <LoadingScreen />}
			<Dialog
				open={openDialog}
				fullWidth={true}
				fullScreen={!isDesktop}
				maxWidth={isDesktop ? "xs" : false}
				disableEscapeKeyDown
			>
				{!isDesktop && (
					<DialogTitle
						sx={{
							backgroundColor: "green",
							p: 2,
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
						}}
					>
						<img src="/zenith/zenith-logo.svg" alt="Zenith Clinics Logo" />
					</DialogTitle>
				)}
				<DialogContent
					sx={{
						display: "flex",
						flexDirection: "column",
						alignItems: "center",
						justifyContent: "space-between",
					}}
				>
					<Grid
						container
						direction={"column"}
						sx={{ width: "100%", flexGrow: 1 }}
						justifyContent={"center"}
						alignItems={"center"}
						textAlign={"center"}
						mt={2}
					>
						<Typography variant="h6" fontWeight={"bold"}>
							Confirm Cancellation <br />
						</Typography>
						<Typography variant="h3" fontWeight={"bold"} mb={2}>
							<span style={{ color: "green" }}>Are you sure ?</span>
						</Typography>
						<img className={styles.cancelImg} src="/zenith/confirm-cancel.png" />
						<Grid
							sx={{ width: "100%", fontSize: "14px", margin: "20px 0px" }}
							textAlign={"start"}
							container
							direction={"column"}
							alignItems={"start"}
						>
							<Grid size={12}>
								<Typography variant="h6" align="center">
									Are you sure you want to{" "}
									<span style={{ fontWeight: "bold" }}>cancel your appointment</span> with Dr. H on{" "}
									{moment(consultDate).format("MMMM Do YYYY")}?{" "}
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%", paddingBottom: "20px" }}
								textAlign={"center"}
								container
								direction={"row"}
								alignItems={"center"}
								justifyContent={"space-evenly"}
							>
								<Grid
									size={12}
									textAlign={"center"}
									container
									direction={"row"}
									alignItems={"center"}
									justifyContent={"space-evenly"}
								>
									<Grid size={5}>
										<Button
											type="submit"
											variant="contained"
											fullWidth
											sx={{ mt: 2, maxWidth: "150px" }}
											disableElevation
											onClick={() => {
												setOpenDialog(false);
											}}
										>
											No
										</Button>
									</Grid>
									<Grid size={5}>
										<Button
											type="submit"
											variant="outlined"
											fullWidth
											sx={{ mt: 2, maxWidth: "150px" }}
											onClick={(e) => handleSubmitConfirm(e)}
										>
											Yes
										</Button>
									</Grid>
								</Grid>
							</Grid>
						</Grid>
					</Grid>
				</DialogContent>
			</Dialog>
		</>
	);
}
