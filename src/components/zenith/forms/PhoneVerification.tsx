import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, TextField, Typography, ThemeProvider, useMediaQuery, InputAdornment } from "@mui/material";
import Grid from "@mui/material/Grid2";
import axios from "axios";
import { AuthUser } from "../../../types";
import LoadingScreen from "../../../utils/loading-screen";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import axiosInstance from "../../../services/axios";
import UserSteps from "../../../types/enum";
import { FlowController, useFlow } from "../../../hooks/flow-controller";

const resendButtonStyle = {
	backgroundColor: "white",
	color: zenithTheme.palette.primary.main,
	borderColor: zenithTheme.palette.primary.main,
};

const inputStyle = {
	backgroundColor: "white",
	borderRadius: "13px",
	"& .MuiOutlinedInput-root": {
		borderRadius: "13px",
		"&:hover": {
			borderColor: "black",
		},
		"&.Mui-focused fieldset": {
			borderColor: "black",
			borderRadius: "13px",
		},
	},
	"& .MuiInputLabel-root": {
		color: "#3B3B3B",
	},
	"& .MuiInputLabel-root.Mui-focused": {
		color: "white",
	},
};

function PhoneVerification() {
	const [canSubmit, setCanSubmit] = useState(false);
	const [canSubmitResend, setCanSubmitResend] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [otp, setOtp] = useState("");
	const { user, setUser } = useFlow();
	const [phoneNumberResend, setPhoneNumberResend] = useState("");

	useEffect(() => {
		function init() {
			if (user?.phone !== undefined) {
				let phoneValue = user.phone;

				// Remove the '04' prefix if it exists
				if (phoneValue.startsWith("04")) {
					phoneValue = phoneValue.substring(2);
				}

				// Limit to 8 digits max
				phoneValue = phoneValue.substring(0, 8);

				setPhoneNumberResend(phoneValue);
			}
			setIsLoading(false);
		}
		init();
	}, [user]);
	const navigate = useNavigate();
	const isDesktopOrMobile = useMediaQuery(zenithTheme.breakpoints.up("sm"));

	const { enqueueSnackbar } = useSnackbar();
	const [formErrorsVerify, setformErrorsVerify] = useState({
		otp: false,
	});
	const [formErrorsResend, setformErrorsResend] = useState({
		phone_number: false,
	});
	async function handleSubmit() {
		setIsLoading(true);
		try {
			const result = await axiosInstance.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/check-otp`,
				{
					otp: otp,
				},
				{ withCredentials: true }
			);
			if (result.data && result.data.success) {
				enqueueSnackbar("You have successfully verified your phone number.", {
					variant: "success",
				});
				user!.phoneverified = true;
				setUser(user);
				navigate({ to: "/patient/questionnaire" });
			}
		} catch (e: any) {
			console.log(e);
			enqueueSnackbar("Phone verification failed", {
				variant: "error",
			});
		} finally {
			setIsLoading(false);
		}
	}

	async function handleSubmitResend() {
		setIsLoading(true);
		try {
			const result = await axiosInstance.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/resend-otp`,
				{
					phone: "04" + phoneNumberResend,
				},
				{ withCredentials: true }
			);
			if (result.data.success) {
				enqueueSnackbar("A new OTP has been sent your phone number : 04" + phoneNumberResend, {
					variant: "success",
				});
				user!.phone = "04" + phoneNumberResend;
				setUser(user as AuthUser);
			}
		} catch (e: any) {
			console.log(e);
			enqueueSnackbar("Resend otp failed", {
				variant: "error",
			});
		} finally {
			setIsLoading(false);
		}
	}

	const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		let phoneValue = e.target.value;

		// Remove non-numeric characters
		phoneValue = phoneValue.replace(/[^0-9]/g, "");

		// Limit to 8 digits (excluding the prefix)
		phoneValue = phoneValue.substring(0, 8);

		setPhoneNumberResend(phoneValue);
	};

	useEffect(() => {
		window.scrollTo(0, 0);
	}, []);

	return (
		<>
			{isLoading && <LoadingScreen />}
			<ThemeProvider theme={zenithTheme}>
				<Stack gap={1} marginBottom={"300px"} sx={{ marginBottom: "250px", width: "100%" }}>
					<Grid container direction={"column"} padding={"20px 0"}>
						<Grid size={12}>
							<Typography variant="body1" fontWeight="bold">
								Pre-Screening
							</Typography>
						</Grid>
						<Grid size={12}>
							<Typography variant="h3" fontWeight="bold" sx={{ color: "green" }}>
								Phone Verification
							</Typography>
						</Grid>
					</Grid>
					<Grid size={12}>
						<img src="/zenith/Phone-verify.png" alt="Phone Verification" />
					</Grid>
					<Grid marginTop={"50px"} marginBottom={"30px"}>
						<Typography align="center" variant="body1">
							The phone number you provide has received a SMS notification with a{" "}
							<span style={{ fontWeight: "bold" }}>4 digits code.</span>
						</Typography>
					</Grid>
					<Grid>
						<Typography align="center" variant="body1">
							Please provide this code to validate your account creation. If you didn't receive any SMS,
							click the button "Resend code".
						</Typography>
					</Grid>
					<Grid width={"100%"} padding={!isDesktopOrMobile ? "20px 20px" : "50px"}>
						<Grid sx={{ width: "100%" }} width={"70%"} textAlign={"center"}>
							<Grid container direction={"column"} alignItems={"start"}>
								<Grid>
									<Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
										OTP code *
									</Typography>
								</Grid>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="otp"
									placeholder="Enter your OTP code"
									size="small"
									onChange={(e) => setOtp(e.target.value)}
									margin="normal"
									fullWidth
									value={otp}
									required={true}
									error={formErrorsVerify.otp}
									helperText={formErrorsVerify.otp ? "Please enter the OTP code" : ""}
								/>
							</Grid>
							<Grid>
								<Button
									type="submit"
									fullWidth
									variant="contained"
									disableElevation
									sx={{ mt: 2 }}
									onClick={handleSubmit}
								>
									Verify your phone number
								</Button>
							</Grid>

							<Grid paddingTop={"50px"} container direction={"column"} alignItems={"start"}>
								<Grid>
									<Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
										Phone number *
									</Typography>
								</Grid>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="phone_number"
									placeholder="Enter your phone number"
									size="small"
									onChange={handlePhoneChange}
									margin="normal"
									fullWidth
									value={phoneNumberResend}
									required={true}
									slotProps={{
										htmlInput: {
											pattern: "[0-9]*",
											inputMode: "numeric",
										},
										input: {
											startAdornment: (
												<InputAdornment
													position="start"
													sx={{
														color: "text.disabled",
														mr: 0,
														"& p": {
															fontSize: "inherit",
															lineHeight: "inherit",
														},
													}}
												>
													04
												</InputAdornment>
											),
											sx: {
												"& .MuiInputAdornment-root": {
													marginRight: 0,
												},
												"& input": {
													paddingLeft: 0,
												},
											},
										},
									}}
									error={formErrorsResend.phone_number}
									helperText={
										formErrorsResend.phone_number
											? "Please enter your phone number"
											: "Phone should start with 04 followed by 8 digits"
									}
								/>
							</Grid>
							<Grid>
								<Button
									type="submit"
									fullWidth
									disableElevation
									variant="outlined"
									sx={{ mt: 2 }}
									onClick={handleSubmitResend}
								>
									Resend code
								</Button>
							</Grid>
						</Grid>
					</Grid>
				</Stack>
			</ThemeProvider>
		</>
	);
}

export default PhoneVerification;
