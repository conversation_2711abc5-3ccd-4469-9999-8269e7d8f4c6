import { <PERSON>, <PERSON>ack, Typo<PERSON>, But<PERSON>, List, ListItemText, styled } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { MakeGenerics, useSearch } from "@tanstack/react-location";
import axios from "axios";
import { useSnackbar } from "notistack";
import { useState } from "react";

type UrlProps = MakeGenerics<{
	Search: {
		token: string;
	};
}>;

const VisuallyHiddenInput = styled("input")({
	clip: "rect(0 0 0 0)",
	clipPath: "inset(50%)",
	height: 1,
	overflow: "hidden",
	position: "absolute",
	bottom: 0,
	left: 0,
	whiteSpace: "nowrap",
	width: 1,
});

const FormHeartRate = () => {
	// get the URL parameters
	const { token } = useSearch<UrlProps>();
	const [selectedFileName, setSelectedFileName] = useState<string | undefined>(undefined);
	const [selectedFile, setSelectedFile] = useState<File | undefined>(undefined);
	const [loading, setLoading] = useState(false);
	const { enqueueSnackbar } = useSnackbar();

	const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSelectedFileName(e?.target?.files?.[0].name);
		setSelectedFile(e?.target?.files?.[0]);
	};

	const handleSubmit = async () => {
		if (!selectedFile) {
			alert("Please select a file before submitting.");
			return;
		}
		setLoading(true);
		try {
			let imageUrl = null;
			if (selectedFile) {
				const response = await axios.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/get-presigned-url`, {
					fileName: selectedFile.name,
					fileType: selectedFile.type,
				});
				const { url, key } = response.data;
				const publicBaseUrl = `https://${import.meta.env.VITE_APP_BUCKET_NAME}.s3.${
					import.meta.env.VITE_APP_AWS_REGION
				}.amazonaws.com/`;
				imageUrl = `${publicBaseUrl}${key}`;
				await fetch(url, {
					method: "PUT",
					headers: {
						"Content-Type": selectedFile.type,
					},
					body: selectedFile,
				});
				await axios.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/upload-form-doc`, {
					file_url: imageUrl,
					question_key: "heart_rate",
					lead_id: token,
				});
				enqueueSnackbar("File uploaded successfully", { variant: "success" });
			}
		} catch (error) {
			enqueueSnackbar("Error uploading file. Please try again.", { variant: "error" });
			console.error("Error uploading file:", error);
		} finally {
			setLoading(false);
		}
	};

	return (
		<Grid container direction={"column"} justifyContent={"flex-start"} maxWidth={500} spacing={2} sx={{ pt: 2 }}>
			<Grid size={12}>
				<Typography variant="h6" color="black" fontWeight="bold">
					Pre-Screening
				</Typography>
				<Typography variant="h4" sx={{ color: "green" }} fontWeight="bold" align="center">
					Upload Your Heart Rate Result
				</Typography>
			</Grid>
			<Grid size={12}>
				<Typography variant="body1" sx={{ color: "#696969" }} align="left">
					Please upload a <span style={{ fontWeight: "bold" }}>clear photo</span> of your results below. This
					helps our doctors make safe, informed decisions during your appointment.
				</Typography>
			</Grid>
			<Grid size={12}>
				<Typography variant="body1" align="left" sx={{ color: "#696969" }}>
					<span style={{ fontWeight: "bold" }}>Make sure the image shows:</span>
				</Typography>
				<Stack justifyContent={"flex-start"}>
					<ul>
						<li style={{ textAlign: "left" }}>Your full name (if recorded)</li>
						<li style={{ textAlign: "left" }}>The date of the reading (within the last 7 days)</li>
						<li style={{ textAlign: "left" }}>Your heart rate reading</li>
					</ul>
				</Stack>
			</Grid>
			<Grid size={12}>
				<Stack
					direction="row"
					alignItems="center"
					spacing={2}
					sx={{ backgroundColor: "#F4F4F4", cursor: "pointer" }}
				>
					<Button
						component="label"
						role={undefined}
						variant="contained"
						sx={{
							backgroundColor: "#D7D7D7",
							color: "black",
							textTransform: "none",
							width: "150px",
							mr: 0.5,
						}}
					>
						Choose File
						<VisuallyHiddenInput
							type="file"
							accept=".pdf,image/png,image/jpg,image/jpeg,image/heic,image/heif,.doc,.docx"
							onChange={handleFileChange}
						/>
					</Button>
					<Typography variant="body2" sx={{ color: "#000", wordBreak: "break-all" }}>
						{selectedFileName || "No file chosen"}
					</Typography>
				</Stack>
			</Grid>
			<Grid size={12}>
				<Typography variant="body1" sx={{ color: "#696969" }} align="left">
					<span style={{ fontWeight: "bold" }}>Note:</span> This is a standard safety step required before
					consultations. Thanks for helping us keep your care tailored and responsible.
				</Typography>
			</Grid>
			<Grid size={12}>
				<Button
					variant="contained"
					sx={{ width: "240px", backgroundColor: "green" }}
					disabled={selectedFile === undefined || loading}
					onClick={handleSubmit}
				>
					{loading ? "Submitting..." : "Submit"}
				</Button>
			</Grid>
		</Grid>
	);
};

export default FormHeartRate;
