import { useEffect, useState } from "react";
import {
	MobileStepper,
	Box,
	Button,
	Stack,
	TextField,
	Typography,
	FormControlLabel,
	Checkbox,
	FormControl,
	FormGroup,
	Radio,
	RadioGroup,
	ThemeProvider,
	Collapse,
} from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";
import { DatePicker } from "@mui/x-date-pickers";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";
import Grid from "@mui/material/Grid2";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

import moment from "moment";
import { Questionnaire } from "../../../types";
import { useNavigate } from "@tanstack/react-location";
import axios from "axios";
import LoadingScreen from "../../../utils/loading-screen";
import { useSnackbar } from "notistack";
import { CheckBox } from "@mui/icons-material";
import zenithTheme from "../../../styles/zenith/theme";
import UserSteps from "../../../types/enum";
import { useFlow } from "../../../hooks/flow-controller";

const inputStyle = {
	backgroundColor: "white",
	borderRadius: "13px",
	/*"& .MuiOutlinedInput-root": {
    borderRadius: "13px",
    "&:hover": {
      borderColor: "black",
    },
    "&.Mui-focused fieldset": {
      borderColor: "black",
      borderRadius: "13px",
    },
  },
  "& .MuiInputLabel-root": {
    color: "#3B3B3B",
  },
  "& .MuiInputLabel-root.Mui-focused": {
    color: "white",
  },*/
};

function FormQuestionnaire() {
	const [activeStep, setActiveStep] = useState(0);
	const [canDoNext, setCanDoNext] = useState(false);
	const [isOlderThanEighteen, setIsOlderThanEighteen] = useState(true);
	const [isLoading, setIsLoading] = useState(false);
	const { enqueueSnackbar } = useSnackbar();
	const { user, setUser } = useFlow();

	const navigate = useNavigate();
	const [formData, setFormData] = useState<Questionnaire>({
		dob: null,
		condition: "",
		first_medication: "",
		second_medication: "",
		children: "",
		disorder: "",
		diseases: "",
		addiction: "",
		treatment: "",
		alternative_medecine: "",
		trial: "",
		gender: "",
		medicare_number: "",
		general_practitioner: "",
		gp_details: {
			gp_name: "",
			gp_practice_name: "",
			gp_contact: "",
		},
		height: "",
		weight: "",
		blood_pressure: "",
		heart_rate: "",
		gpConsent: false,
		medicare_index_number: "",
	});
	const [formErrors, setFormErrors] = useState({
		dob: false,
		condition: false,
		children: false,
		disorder: false,
		diseases: false,
		addiction: false,
		treatment: false,
		trial: false,
		gender: false,
		general_practitioner: false,
		gp_details: {
			gp_name: false,
			gp_practice_name: false,
			gp_contact: false,
		},
		medicare_number: false,
		height: false,
		weight: false,
		blood_pressure: false,
		heart_rate: false,
		gpConsent: false,
	});

	const handleSteps = (step?: number, value?: string) => {
		if (activeStep === 0) {
			if (
				formData.dob &&
				formData.condition !== "" &&
				formData.gender !== "" &&
				formData.alternative_medecine !== ""
			) {
				setCanDoNext(true);
			} else {
				setCanDoNext(false);
			}
		}

		if (activeStep === 1) {
			if (formData.children !== "" && formData.disorder !== "" && formData.diseases !== "") {
				setCanDoNext(true);
			} else {
				setCanDoNext(false);
			}
		}

		if (activeStep === 2) {
			if (formData.addiction !== "" && formData.treatment !== "" && formData.trial !== "") {
				setCanDoNext(true);
			}

			if (step === 7 && value) {
				setCanDoNext(true);
			}
		}
		if (activeStep === 3) {
			setCanDoNext(true);
		}
		if (activeStep === 4) {
			if (
				formData.heart_rate !== "" &&
				formData.blood_pressure !== "" &&
				formData.weight !== "" &&
				formData.height !== ""
			) {
				setCanDoNext(true);
			} else {
				setCanDoNext(false);
			}
		}
		if (activeStep === 5) {
			if (formData.general_practitioner !== "") {
				setCanDoNext(true);
			} else {
				setCanDoNext(false);
			}
		}

		if (activeStep === 6) {
			const contact = formData.gp_details.gp_contact;
			const phoneRegex = /^04\d{8}$/;
			const emailRegex = /^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$/;

			if (
				formData.gp_details.gp_name !== "" &&
				formData.gp_details.gp_practice_name !== "" &&
				(contact.match(phoneRegex) || contact.match(emailRegex)) &&
				formData.gpConsent
			) {
				setCanDoNext(true);
			} else {
				setCanDoNext(false);
			}
		}
		if (activeStep === 7) {
			if (
				formData.medicare_number !== "" &&
				formData.medicare_index_number !== "" &&
				formData.first_medication !== "" &&
				formData.second_medication !== ""
			) {
				setCanDoNext(true);
			}
		}
	};

	useEffect(() => {
		console.log("activeStep:", activeStep);
		handleSteps();
	}, [
		formData.gender,
		formData.condition,
		formData.alternative_medecine,
		formData.diseases,
		formData.children,
		formData.disorder,
		formData.blood_pressure,
		formData.heart_rate,
		formData.height,
		formData.weight,
		formData.general_practitioner,
		formData.gp_details,
		formData.gpConsent,
		formData.first_medication,
	]);
	const handleChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { name, value } = event.target;
		if (name.startsWith("gp_details.")) {
			const key = name.split(".")[1];
			setFormData({ ...formData, gp_details: { ...formData.gp_details, [key]: value } });
		} else {
			setFormData({ ...formData, [name]: value });
		}
		if (event.target.validity.valid) {
			setFormErrors({ ...formErrors, [name]: false });
		} else {
			setFormErrors({ ...formErrors, [name]: true });
		}
	};

	const handleDateChange = (date: moment.Moment | null) => {
		const formattedDate = moment(date, "DD-MM-YYYY");
		const years = moment().diff(formattedDate, "years", false);

		if (years >= 18) {
			setFormData({ ...formData, dob: formattedDate });
			handleSteps();
			setIsOlderThanEighteen(true);
		} else {
			setIsOlderThanEighteen(false);
			handleSteps();
			setCanDoNext(false);
		}
	};

	const handleSubmit = async () => {
		const formattedDateString = moment(formData.dob).format("YYYY-MM-DD");
		const formDataFormatted = {
			...formData,
			dob: formattedDateString,
		};
		setIsLoading(true);
		try {
			const result = await axios.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/questionnaire/upload`,
				formDataFormatted,
				{ withCredentials: true }
			);
			if (result.data) {
				fireMetaPixelEvent();
				if (result.data.isValid) {
					enqueueSnackbar("Questionnaire Submitted", {
						variant: "success",
					});
					user!.laststep = UserSteps.QUESTIONNAIRE;
					setUser(user);
					if (!result.data.softReject) {
						navigate({ to: "/patient/discharge-letter" });
					} else {
						navigate({ to: "/patient/pending-review" });
					}
				} else {
					navigate({ to: "/patient/not-approved" });
					// setOpenNotApproved(true);
				}
			} else {
				enqueueSnackbar("Failed to submit Questionnaire", {
					variant: "error",
				});
				user!.laststep = UserSteps.QUESTIONNAIRE;
				setUser(user);

				// add delay to ensure meta pixel fires before redirect
				const timer = setTimeout(() => {
					navigate({ to: "/patient/discharge-letter" });
				}, 1000);
			}
		} catch (e) {
			enqueueSnackbar("Failed to submit Questionnaire", {
				variant: "error",
			});
			throw e;
		} finally {
			setIsLoading(false);
		}
	};

	const handleNext = () => {
		setCanDoNext(false);
		if (activeStep === 5 && formData.general_practitioner === "no") {
			setActiveStep((prevActiveStep) => prevActiveStep + 1);
		}
		setActiveStep((prevActiveStep) => prevActiveStep + 1);
	};

	const handleBack = () => {
		setCanDoNext(true);
		if (activeStep === 7 && formData.general_practitioner === "no") {
			setActiveStep((prevActiveStep) => prevActiveStep - 1);
		}
		setActiveStep((prevActiveStep) => prevActiveStep - 1);
	};

	const fireMetaPixelEvent = () => {
		// @ts-ignore
		if (typeof window.fbq !== "undefined") {
			// @ts-ignore
			window.fbq("trackSingleCustom", "2897882420379934", "QuestionnaireDone", {
				source: "ZenithQuestionnaire",
			});
			// @ts-ignore
			window.fbq("trackSingleCustom", "1107806040959291", "QuestionnaireDone", {
				source: "ZenithQuestionnaire",
			});
		} else {
			console.warn("Meta Pixel not initialized");
		}
	};

	useEffect(() => {
		handleSteps();
	}, [activeStep]);
	function redirectToSchedule() {
		axios
			.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/encrypt-id`, { user }, { withCredentials: true })
			.then((response) => {
				const encryptedLeadID = response.data.data.leadID;
				const baseUrl = `${import.meta.env.VITE_DRUI_URL}/schedule`;
				const url = `${baseUrl}?token=${encryptedLeadID}`;
				window.location.href = url;
			});
	}
	const steps = [
		{
			label: "Step 1",
			content: (
				<Stack sx={{ pb: 3 }} spacing={2}>
					<Grid container direction={"column"} justifyContent={"center"} alignItems={"center"}>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
								width: "100%",
								display: "flex",
								flexDirection: "column",
								justifyContent: "flex-start",
								alignItems: "flex-start",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										color: "white",
										mb: 1,
										fontWeight: "400",
									}}
									align="left"
								>
									What is your date of birth? *
								</Typography>
							</Grid>
							<LocalizationProvider dateAdapter={AdapterMoment}>
								<DatePicker
									views={["year", "month", "day"]}
									sx={{ ...inputStyle, width: "100%" }}
									name="dob"
									value={formData.dob}
									slotProps={{
										textField: {
											size: "small",
											error: isOlderThanEighteen ? false : true,
											helperText: isOlderThanEighteen ? undefined : "Must be older than 18",
										},
									}}
									onChange={handleDateChange}
								/>
							</LocalizationProvider>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								mt: 2,
								borderRadius: "13px",
								width: "100%",
							}}
						>
							<Grid
								container
								direction={"column"}
								justifyContent={"flex-start"}
								alignItems={"flex-start"}
							>
								<Grid sx={{ width: "100%" }}>
									<Typography
										sx={{
											fontSize: "16px",
											color: "white",
											mb: 1,
											fontWeight: "400",
										}}
										align="left"
									>
										What condition or symptom are you having issues with? *
									</Typography>
								</Grid>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="condition"
									autoComplete="off"
									size="small"
									onChange={handleChange}
									margin="normal"
									fullWidth
									value={formData.condition}
								/>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								mt: 2,
								borderRadius: "13px",
								width: "100%",
							}}
						>
							<Grid container direction={"column"} justifyContent="flex-start" alignItems={"flex-start"}>
								<Grid size={12} sx={{ alignItems: "start" }}>
									<Typography
										sx={{
											fontSize: "16px",
											color: "white",
											mb: 1,
											fontWeight: "400",
										}}
										align="left"
									>
										What was your Gender at Birth? *
									</Typography>
								</Grid>
								<Grid size={12} container>
									<RadioGroup value={formData.gender} name="gender" onChange={handleChange}>
										<Grid
											container
											direction="column"
											justifyContent="flex-start"
											alignItems="flex-start"
										>
											<Grid container justifyContent={"flex-start"} alignItems="center" size={12}>
												<FormControlLabel
													value="male"
													id="male"
													control={
														<Radio
															sx={{
																"&.Mui-checked, &.MuiRadio-root": {
																	color: "white",
																	borderRadius: 1,
																	backgroundColor: "white",
																	width: "20px",
																	height: "20px",
																	m: 1,
																},
															}}
															checked={formData.gender == "male"}
															onClick={(e) => handleChange}
															checkedIcon={
																<CheckIcon
																	sx={{ color: "green", fontSize: "medium" }}
																/>
															}
														/>
													}
													label={
														<span style={{ color: "#ffffff", fontSize: "0.8em" }}>
															Male
														</span>
													}
												/>
											</Grid>
											<Grid container justifyContent={"flex-start"} alignItems="center" size={12}>
												<FormControlLabel
													value="female"
													id="female"
													control={
														<Radio
															sx={{
																"&.Mui-checked, &.MuiRadio-root": {
																	color: "white",
																	borderRadius: 1,
																	backgroundColor: "white",
																	width: "20px",
																	height: "20px",
																	m: 1,
																},
															}}
															checked={formData.gender == "female"}
															onClick={(e) => handleChange}
															checkedIcon={
																<CheckIcon
																	sx={{ color: "green", fontSize: "medium" }}
																/>
															}
														/>
													}
													label={
														<span style={{ color: "#ffffff", fontSize: "0.8em" }}>
															Female
														</span>
													}
												/>
											</Grid>
										</Grid>
									</RadioGroup>
								</Grid>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								mt: 2,
								borderRadius: "13px",
								width: "100%",
							}}
						>
							<Grid container direction={"column"} justifyContent="flex-start" alignItems={"flex-start"}>
								<Grid size={12}>
									<Typography
										sx={{
											fontSize: "16px",
											color: "white",
											mb: 1,
											fontWeight: "400",
										}}
										align="left"
									>
										Have you used alternative medicine before? *
									</Typography>
								</Grid>
								<Grid size={12} container justifyContent="flex-start" alignItems={"flex-start"}>
									<RadioGroup
										value={formData.alternative_medecine}
										name="alternative_medecine"
										onChange={handleChange}
										sx={{ width: "100%" }}
									>
										<Stack
											justifyContent={"flex-start"}
											alignItems={"flex-start"}
											direction="column"
											sx={{ width: "100%" }}
										>
											<FormControlLabel
												value="medically"
												id="medically"
												control={
													<Radio
														sx={{
															"&.Mui-checked, &.MuiRadio-root": {
																color: "white",
																borderRadius: 1,
																backgroundColor: "white",
																width: "20px",
																height: "20px",
																m: 1,
															},
														}}
														checked={formData.alternative_medecine == "medically"}
														onClick={(e) => handleChange}
														checkedIcon={
															<CheckIcon sx={{ color: "green", fontSize: "medium" }} />
														}
													/>
												}
												label={
													<span style={{ color: "#ffffff", fontSize: "0.8em" }}>
														Medically
													</span>
												}
											/>
											<FormControlLabel
												value="recreationally"
												id="recreationally"
												control={
													<Radio
														sx={{
															"&.Mui-checked, &.MuiRadio-root": {
																color: "white",
																borderRadius: 1,
																backgroundColor: "white",
																width: "20px",
																height: "20px",
																m: 1,
															},
														}}
														checked={formData.alternative_medecine == "recreationally"}
														onClick={(e) => handleChange}
														checkedIcon={
															<CheckIcon sx={{ color: "green", fontSize: "medium" }} />
														}
													/>
												}
												label={
													<span style={{ color: "#ffffff", fontSize: "0.8em" }}>
														Recreationally
													</span>
												}
											/>
											<FormControlLabel
												value="no"
												id="alternative_no"
												control={
													<Radio
														sx={{
															"&.Mui-checked, &.MuiRadio-root": {
																color: "white",
																borderRadius: 1,
																backgroundColor: "white",
																width: "20px",
																height: "20px",
																m: 1,
															},
														}}
														checked={formData.alternative_medecine == "no"}
														onClick={(e) => handleChange}
														checkedIcon={
															<CheckIcon sx={{ color: "green", fontSize: "medium" }} />
														}
													/>
												}
												label={<span style={{ color: "#ffffff", fontSize: "0.8em" }}>No</span>}
											/>
										</Stack>
									</RadioGroup>
								</Grid>
							</Grid>
						</Box>
					</Grid>
				</Stack>
			),
		},
		{
			label: "Step 2",
			content: (
				<>
					<Grid container direction={"column"}>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Are you planning to have children within the next 6 months? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												children: "yes",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.children === "yes" ? "#515151" : "white",
										color: formData.children === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												children: "no",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.children === "no" ? "#515151" : "white",
										color: formData.children === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Do you suffer from psychosis, bipolar disorder or schizophrenia? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												disorder: "yes",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.disorder === "yes" ? "#515151" : "white",
										color: formData.disorder === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												disorder: "no",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.disorder === "no" ? "#515151" : "white",
										color: formData.disorder === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Do you suffer from any cardiovascular diseases, including irregular heartbeat
									(arrhythmia)? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												diseases: "yes",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.diseases === "yes" ? "#515151" : "white",
										color: formData.diseases === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												diseases: "no",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.diseases === "no" ? "#515151" : "white",
										color: formData.diseases === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
					</Grid>
				</>
			),
		},
		{
			label: "Step 3",
			content: (
				<>
					<Grid container direction={"column"}>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Do you have an addiction to any psychoactive substances and/or drugs, including
									alcohol, but excluding nicotine and caffeine? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												addiction: "yes",
											};
										});

										handleSteps(5, "yes");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.addiction === "yes" ? "#515151" : "white",
										color: formData.addiction === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												addiction: "no",
											};
										});

										handleSteps(5, "no");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.addiction === "no" ? "#515151" : "white",
										color: formData.addiction === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Have you discussed other treatment options with your doctor? Including medical and
									conservative therapies. *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												treatment: "yes",
											};
										});

										handleSteps(6, "yes");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.treatment === "yes" ? "#515151" : "white",
										color: formData.treatment === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												treatment: "no",
											};
										});

										handleSteps(6, "no");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.treatment === "no" ? "#515151" : "white",
										color: formData.treatment === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Knowing the alternative management options, do you still want to trial Alternative
									Medicine as a treatment option for your condition? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												trial: "yes",
											};
										});

										handleSteps(7, "yes");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.trial === "yes" ? "#515151" : "white",
										color: formData.trial === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												trial: "no",
											};
										});

										handleSteps(7, "no");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.trial === "no" ? "#515151" : "white",
										color: formData.trial === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
					</Grid>
				</>
			),
		},
		{
			label: "Step 4",
			content: (
				<>
					<Stack spacing={1} maxWidth={500} p={2}>
						<Typography variant="h5" color="green" fontWeight="bold">
							Important Update
						</Typography>
						<Typography variant="h2" fontWeight="bold" sx={{ color: "#515151" }}>
							New Rules for Doctors
						</Typography>
						<Typography variant="body1" sx={{ color: "#515151" }}>
							The rules for prescribing alternative medicine (like medicinal cannabis) have changed.
						</Typography>
						<Typography variant="body1" sx={{ color: "#515151" }}>
							Doctors must now follow new AHPRA guidelines to keep you safe and ensure the treatment is
							right for you.
						</Typography>
						<Typography variant="body1" sx={{ color: "#515151" }}>
							For more information, visit:{" "}
							<a
								style={{ color: "#515151", textDecoration: "underline", wordWrap: "break-word" }}
								target="_blank"
								href="https://www.ahpra.gov.au/Resources/Medicinal-cannabis-prescribing.aspx"
							>
								https://www.ahpra.gov.au/Resources/Medicinal-cannabis-prescribing.aspx
							</a>
						</Typography>
						<Typography variant="body1" sx={{ color: "#515151" }}>
							If you&apos;re unsure what to do next, just follow the steps in the form or contact our
							support team. We&apos;re here to help.
						</Typography>
					</Stack>
				</>
			),
		},
		{
			label: "Step 5",
			content: (
				<>
					<Grid container direction={"column"}>
						<Grid size={12}>
							<Box
								boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
								sx={{
									mt: 1,
									backgroundColor: "green",
									padding: 2,
									borderRadius: "13px",
								}}
							>
								<Typography variant="body1" align="left" fontWeight={400} color="white">
									Do you have any personal or family history of high blood pressure?
								</Typography>
								<Typography variant="body1" align="left" fontWeight={200} color="white" mb={2}>
									(Note: If your My Health Record indicates high blood pressure, you will be required
									to complete a free blood pressure assessment prior to issuing any treatment plans.)
								</Typography>
								<Stack direction="row" justifyContent={"space-between"} alignItems="center">
									<Button
										variant="contained"
										sx={{
											backgroundColor: formData.blood_pressure === "yes" ? "#BDBDBD" : "white",
											color: formData.blood_pressure === "yes" ? "red" : "black",
											border: formData.blood_pressure === "yes" ? "1px solid red" : "none",
											width: "35%",
											borderRadius: 10,
											fontWeight: "bold",
										}}
										onClick={() => {
											setFormData((prev) => {
												return {
													...prev,
													blood_pressure: "yes",
												};
											});

											handleSteps(7, "yes");
										}}
									>
										Yes
									</Button>
									<Button
										variant="contained"
										sx={{
											backgroundColor: formData.blood_pressure === "no" ? "#515151" : "white",
											color: formData.blood_pressure === "no" ? "white" : "black",
											width: "35%",
											borderRadius: 10,
											fontWeight: "bold",
										}}
										onClick={() => {
											setFormData((prev) => {
												return {
													...prev,
													blood_pressure: "no",
												};
											});

											handleSteps(7, "yes");
										}}
									>
										No
									</Button>
								</Stack>
							</Box>
							<Collapse in={formData.blood_pressure === "yes"}>
								<Box
									sx={{
										border: "2px solid green",
										borderRadius: "0 0 14px 14px",
										padding: 2,
										mt: -2,
										pt: 3,
									}}
								>
									<Typography
										variant="body1"
										align="left"
										fontWeight={400}
										color="black"
										gutterBottom
									>
										You are required to go to your local pharmacy for a free blood pressure
										assessment prior to your consultation.
									</Typography>
									<Typography
										variant="body1"
										align="left"
										fontWeight={400}
										color="black"
										gutterBottom
									>
										<span style={{ fontWeight: "bold" }}>Please send us an image</span> of your
										result via email or sms before your consultation.{" "}
										<span style={{ fontWeight: "bold" }}>
											We will provide a secure link to upload your result.
										</span>
									</Typography>
								</Box>
							</Collapse>
						</Grid>
						<Grid size={12}>
							<Box
								boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
								sx={{
									mt: 1,
									backgroundColor: "green",
									padding: 2,
									borderRadius: "13px",
								}}
							>
								<Typography variant="body1" align="left" fontWeight={400} color="white">
									Do you have any personal or family history of an increased heart rate (tachycardia)?
								</Typography>
								<Typography variant="body1" align="left" fontWeight={200} color="white" mb={2}>
									(Note: If your My Health Record indicates this, you will be required to submit a
									heart rate measurement prior to treatment.)
								</Typography>
								<Stack direction="row" justifyContent={"space-between"} alignItems="center">
									<Button
										variant="contained"
										sx={{
											backgroundColor: formData.heart_rate === "yes" ? "#BDBDBD" : "white",
											color: formData.heart_rate === "yes" ? "red" : "black",
											border: formData.heart_rate === "yes" ? "1px solid red" : "none",
											width: "35%",
											borderRadius: 10,
											fontWeight: "bold",
										}}
										onClick={() => {
											setFormData((prev) => {
												return {
													...prev,
													heart_rate: "yes",
												};
											});

											handleSteps(7, "yes");
										}}
									>
										Yes
									</Button>
									<Button
										variant="contained"
										sx={{
											backgroundColor: formData.heart_rate === "no" ? "#515151" : "white",
											color: formData.heart_rate === "no" ? "white" : "black",
											width: "35%",
											borderRadius: 10,
											fontWeight: "bold",
										}}
										onClick={() => {
											setFormData((prev) => {
												return {
													...prev,
													heart_rate: "no",
												};
											});

											handleSteps(7, "no");
										}}
									>
										No
									</Button>
								</Stack>
							</Box>
							<Collapse in={formData.heart_rate === "yes"}>
								<Box
									sx={{
										border: "2px solid green",
										borderRadius: "0 0 14px 14px",
										padding: 2,
										mt: -2,
										pt: 3,
									}}
								>
									<Typography
										variant="body1"
										align="left"
										fontWeight={400}
										color="black"
										gutterBottom
									>
										Please obtain your heart rate reading prior to your consultation. You may:
									</Typography>
									<Typography
										variant="body1"
										align="left"
										fontWeight={400}
										color="black"
										gutterBottom
									>
										Visit your local pharmacy for a free heart rate check OR use the Instant Heart
										Rate: HR Monitor app by Azumio on your smartphone.
									</Typography>
									<Typography
										variant="body1"
										align="left"
										fontWeight={400}
										color="black"
										gutterBottom
									>
										<span style={{ fontWeight: "bold" }}>Please send us an image</span> of your
										result via email or sms before your consultation.{" "}
										<span style={{ fontWeight: "bold" }}>
											We will provide a secure link to upload your result.
										</span>
									</Typography>
								</Box>
							</Collapse>
						</Grid>
						<Grid size={12}>
							<Box
								boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
								sx={{
									mt: 1,
									backgroundColor: "green",
									padding: 2,
									borderRadius: "13px",
								}}
							>
								<Typography variant="body1" align="left" fontWeight={400} color="white">
									What is your height?
								</Typography>
								<Typography variant="body1" align="left" fontWeight={200} color="white" mb={2}>
									(Please enter in centimetres)
								</Typography>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="height"
									size="small"
									autoComplete="off"
									onChange={(e) => {
										let value = e.target.value.replace(/[^0-9]/g, "");
										if (value.length > 3) value = value.slice(0, 3);
										setFormData((prev) => ({
											...prev,
											height: value,
										}));
									}}
									placeholder="Height (cm)"
									margin="normal"
									fullWidth
									value={formData.height}
									slotProps={{ htmlInput: { inputMode: "numeric", pattern: "[0-9]*", maxLength: 3 } }}
								/>
							</Box>
						</Grid>
						<Grid size={12}>
							<Box
								boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
								sx={{
									mt: 1,
									backgroundColor: "green",
									padding: 2,
									borderRadius: "13px",
								}}
							>
								<Typography variant="body1" align="left" fontWeight={400} color="white">
									What is your current weight?
								</Typography>
								<Typography variant="body1" align="left" fontWeight={200} color="white" mb={2}>
									(Please enter in kilograms)
								</Typography>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="weight"
									size="small"
									autoComplete="off"
									onChange={(e) => {
										let value = e.target.value.replace(/[^0-9]/g, "");
										if (value.length > 3) value = value.slice(0, 3);
										setFormData((prev) => ({
											...prev,
											weight: value,
										}));
									}}
									placeholder="Weight (kg)"
									margin="normal"
									fullWidth
									value={formData.weight}
									slotProps={{ htmlInput: { inputMode: "numeric", pattern: "[0-9]*", maxLength: 3 } }}
								/>
							</Box>
						</Grid>
					</Grid>
				</>
			),
		},
		{
			label: "Step 6",
			content: (
				<>
					<Grid container direction={"column"}>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 1,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Do you have a regular GP or specialist who manages your care?
								</Typography>
							</Grid>
							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												general_practitioner: "yes",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.general_practitioner === "yes" ? "#515151" : "white",
										color: formData.general_practitioner === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												general_practitioner: "no",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.general_practitioner === "no" ? "#515151" : "white",
										color: formData.general_practitioner === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
					</Grid>
				</>
			),
		},
		{
			label: "Step 7",
			content: (
				<>
					<Grid container direction={"column"} spacing={2}>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 1,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
								width: "100%",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									What is the name of your GP or specialist?
								</Typography>
							</Grid>
							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="gp_details.gp_name"
									size="small"
									autoComplete="off"
									onChange={handleChange}
									placeholder="GP or Specialist Name"
									margin="normal"
									fullWidth
									value={formData.gp_details.gp_name}
								/>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 1,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
								width: "100%",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									What is the Clinic or Practice name?
								</Typography>
							</Grid>
							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="gp_details.gp_practice_name"
									size="small"
									autoComplete="off"
									onChange={handleChange}
									placeholder="Clinic or Practice Name"
									margin="normal"
									fullWidth
									value={formData.gp_details.gp_practice_name}
								/>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 1,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									What is the Contact number or email?
								</Typography>
							</Grid>
							<Grid container justifyContent={"flex-start"} sx={{ width: "100%" }}>
								<Typography variant="caption" align="left" sx={{ color: "white", mb: 1 }}>
									The contact number should not have the country code, just the area code and number
									e.g 0412345678
								</Typography>
							</Grid>
							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="gp_details.gp_contact"
									size="small"
									autoComplete="off"
									onChange={(e) => {
										let value = e.target.value;
										// If it's a phone number, strip non-digits and limit to 10 digits
										if (/^04\d{0,9}$/.test(value.replace(/\D/g, ""))) {
											value = value.replace(/\D/g, "").slice(0, 10);
										}
										setFormData((prev) => ({
											...prev,
											gp_details: {
												...prev.gp_details,
												gp_contact: value,
											},
										}));
									}}
									placeholder="Contact Number or Email"
									margin="normal"
									fullWidth
									value={formData.gp_details.gp_contact}
									slotProps={{
										htmlInput: {
											pattern:
												"^((04\\d{8,10})|([a-zA-Z0-9._%+\\-]+@[a-zA-Z0-9.\\-]+\\.[a-zA-Z]{2,}))$",
											title: "Please enter a valid Australian mobile number (max 10 digits) or email address",
											maxLength: 50,
										},
									}}
								/>
							</Grid>
						</Box>
						<Grid size={12}>
							<Stack spacing={2} direction="row" alignItems="center" p={2}>
								<Checkbox
									checked={formData.gpConsent}
									onChange={(e) => setFormData({ ...formData, gpConsent: e.target.checked })}
								/>
								<Typography variant="caption" align="left" sx={{ color: "#2C2C2C" }}>
									I give permission for the clinic to contact my usual treating doctor to coordinate
									care and share relevant medical information if needed.
								</Typography>
							</Stack>
						</Grid>
					</Grid>
				</>
			),
		},
		{
			label: "Step 8",
			content: (
				<>
					<Grid container direction={"column"}>
						<Typography sx={{ fontSize: "12px", color: "grey" }}>
							In order to be suitable for Alternative Medicine, we need to identify whether you have
							attempted to treat your condition in at least two different ways, over a three month period.
						</Typography>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 1,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
								width: "100%",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Please add the first medication, treatment or therapy you trialled.*
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="first_medication"
									size="small"
									autoComplete="off"
									onChange={handleChange}
									placeholder="Your medication, treatment..."
									margin="normal"
									fullWidth
									value={formData.first_medication}
								/>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
								width: "100%",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Please add the second medication, treatment or therapy you trialled.*
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="second_medication"
									placeholder="Your medication, treatment..."
									size="small"
									autoComplete="off"
									onChange={handleChange}
									margin="normal"
									fullWidth
									value={formData.second_medication}
								/>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
								width: "100%",
								display: "flex",
								flexDirection: "column",
								justifyContent: "flex-start",
								alignItems: "flex-start",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Medicare Card Number
									<br />
									<span style={{ fontWeight: 300 }}>(as shown on your Medicare card)</span>
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="medicare_number"
									size="small"
									autoComplete="off"
									onChange={(e) => {
										let value = e.target.value.replace(/[^0-9]/g, "");
										if (value.length > 10) value = value.slice(0, 10);
										setFormData((prev) => ({
											...prev,
											medicare_number: value,
										}));
									}}
									placeholder="Your Medicare Card Number"
									margin="normal"
									fullWidth
									value={formData.medicare_number}
									slotProps={{
										htmlInput: { inputMode: "numeric", pattern: "[0-9]*", maxLength: 10 },
									}}
								/>
							</Grid>
							<Grid sx={{ width: "100%" }} mt={1}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Individual Reference Number (IRN)
									<br />
									<span style={{ fontWeight: 300 }}>
										(The number next to your name on the Medicare card)
									</span>
								</Typography>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="medicare_index_number"
									size="small"
									autoComplete="off"
									onChange={(e) => {
										const value = e.target.value.replace(/[^0-9]/g, "");
										setFormData((prev) => ({
											...prev,
											medicare_index_number: value,
										}));
									}}
									placeholder="Your Individual Reference Number"
									margin="normal"
									fullWidth
									value={formData.medicare_index_number}
									slotProps={{ htmlInput: { inputMode: "numeric", pattern: "[0-9]*" } }}
								/>
							</Grid>
						</Box>
						<Grid size={12}>
							<Typography sx={{ color: "gray" }} variant="body1" fontWeight={500} mt={2}>
								*For clinical assessment purposes, please list the specific names of the treatments or
								medications you have previously trialled.
							</Typography>
						</Grid>
					</Grid>
				</>
			),
		},
	];

	return (
		<>
			{isLoading && <LoadingScreen />}
			<ThemeProvider theme={zenithTheme}>
				<Stack justifyContent={"center"} gap={2}>
					<Grid container direction={"column"} padding={"20px 0"}>
						<Grid>
							<Typography
								sx={{
									fontSize: "34px",
									fontWeight: "bold",
									lineHeight: "1em",
									color: "green",
								}}
							>
								Pre-Screening
							</Typography>
						</Grid>
					</Grid>
					<MobileStepper
						variant="progress"
						steps={steps.length}
						position="static"
						activeStep={activeStep}
						sx={{
							flexGrow: 1,
							"& .MuiMobileStepper-progress": {
								backgroundColor: "#A5A5A5",
								height: "15px",
								width: "100%",
								borderRadius: "8px",
							},
						}}
						nextButton={
							<Button
								style={{ borderRadius: "13px", display: "none" }}
								variant="contained"
								onClick={handleNext}
								disabled={activeStep === steps.length - 1 || !canDoNext}
							>
								<ArrowForwardIcon />
							</Button>
						}
						backButton={
							<Button
								style={{ borderRadius: "13px", display: "none" }}
								variant="contained"
								onClick={handleBack}
								disabled={activeStep === 0}
							>
								<ArrowBackIcon />
							</Button>
						}
					/>
					<center>
						<Box>
							{steps[activeStep].content}
							{activeStep < steps.length - 1 && (
								<Button
									onClick={handleNext}
									variant="contained"
									fullWidth
									sx={{ mt: 2, maxWidth: 250 }}
									disabled={!canDoNext}
									disableElevation
								>
									Continue
								</Button>
							)}
							{activeStep === steps.length - 1 &&
							formData.addiction !== "" &&
							formData.first_medication !== "" &&
							formData.second_medication !== "" &&
							formData.medicare_number.length > 9 &&
							formData.medicare_index_number.length > 0 ? (
								<Button
									type="submit"
									fullWidth
									variant="contained"
									sx={{ mt: 2 }}
									onClick={handleSubmit}
								>
									Submit
								</Button>
							) : null}
						</Box>
					</center>
					{/* <Typography sx={{ fontSize: "12px", color: "grey", mt: 3, borderTop: "1px solid #131313" }}>
						Any information you provide today is confidential and compliant with the Medical Board of
						Australia Good Medical Practice code, RACGP Standards of General Practice and our Medical
						Confidentiality Duty of Conduct for doctors in Australia, which means we protect your privacy
						and right to confidentiality.
					</Typography> */}
				</Stack>
			</ThemeProvider>
		</>
	);
}

export default FormQuestionnaire;
