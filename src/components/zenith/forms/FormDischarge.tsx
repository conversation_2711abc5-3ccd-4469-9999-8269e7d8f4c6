import React, { useEffect, useState } from "react";
import { styled, ThemeProvider } from "@mui/material/styles";
import {
	FormControl,
	FormControlLabel,
	Stack,
	Button,
	Radio,
	RadioGroup,
	Typography,
	Dialog,
	DialogContent,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import CheckIcon from "@mui/icons-material/Check";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { useNavigate } from "@tanstack/react-location";
import axios from "axios";
import LoadingScreen from "../../../utils/loading-screen";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import UserSteps from "../../../types/enum";
import { useFlow } from "../../../hooks/flow-controller";

// Add interface for member status
interface UserDetails {
	member_status?: string;
	email?: string;
	[key: string]: any;
}

const VisuallyHiddenInput = styled("input")({
	clip: "rect(0 0 0 0)",
	clipPath: "inset(50%)",
	height: 1,
	overflow: "hidden",
	position: "absolute",
	bottom: 0,
	left: 0,
	whiteSpace: "nowrap",
	width: 1,
});

function FormDischarge() {
	const [status, setStatus] = useState(0); // 0: show, 1: hide, 2: hide
	const [selectedFileName, setSelectedFileName] = useState<string | undefined>(undefined);
	const [selectedFile, setSelectedFile] = useState<File | undefined>(undefined);
	const navigate = useNavigate();
	const [isLoading, setIsLoading] = useState(false);
	const { enqueueSnackbar } = useSnackbar();
	const { user, setUser } = useFlow();
	const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
	const [openSuccessDialog, setOpenSuccessDialog] = useState(false);

	// Check authentication and fetch member status
	useEffect(() => {
		const checkAuthAndFetchStatus = async () => {
			// First check authentication
			const isAuthenticated = localStorage.getItem("zenith_authenticated") === "true";

			if (!isAuthenticated) {
				// Store current path in URL parameters instead of localStorage
				const currentPath = "/patient/discharge-letter";
				const loginUrl =
					(import.meta.env.VITE_ZENITH_LOGIN_URL || "/patient/login") +
					`?return_to=${encodeURIComponent(currentPath)}`;
				window.location.href = loginUrl;
				return;
			}

			// Fetch member status if authenticated
			try {
				setIsLoading(true);
				const email = user?.email;

				if (!email) {
					enqueueSnackbar("Could not determine your email. Please try logging in again.", {
						variant: "error",
					});
					return;
				}

				// Fetch lead details from Zoho just to store the status
				const leadResponse = await axios.get(
					`${import.meta.env.VITE_API_URL}/zoho/v1.0/leads/by-email?email=${encodeURIComponent(email)}`,
					{ withCredentials: true }
				);

				if (leadResponse.data?.success) {
					setUserDetails(leadResponse.data);
				}
			} catch (error) {
				console.error("Error fetching member status:", error);
			} finally {
				setIsLoading(false);
			}
		};

		checkAuthAndFetchStatus();
	}, [user, enqueueSnackbar]);

	// Add effect to monitor userDetails changes
	useEffect(() => {
		if (userDetails) {
		}
	}, [userDetails]);

	const radioHandler = (status: number) => {
		setStatus(status);
	};

	function redirectToSchedule() {
		axios
			.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/encrypt-id`, { user }, { withCredentials: true })
			.then((response) => {
				const encryptedLeadID = response.data.data.leadID;
				const baseUrl = `${import.meta.env.VITE_DRUI_URL}/schedule`;
				const url = `${baseUrl}?token=${encryptedLeadID}`;
				window.location.href = url;
			});
	}
	const handleSubmit = async () => {
		setIsLoading(true);
		const formData = new FormData();
		if (selectedFile) {
			formData.append("file", selectedFile);
			try {
				await axios.post(
					`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/discharge-letter/upload`,
					formData,
					{ withCredentials: true }
				);
				if (userDetails?.member_status === "9b - Approve Subject To Discharge") {
					setOpenSuccessDialog(true);
				} else {
					enqueueSnackbar("Discharge Letter Submitted", {
						variant: "success",
					});

					if (user == null) {
						enqueueSnackbar("No user connected found", {
							variant: "error",
						});
					} else {
						user!.laststep = UserSteps.DISCHARGE;
						setUser(user);
						// Proceed to you have been approved
						navigate({ to: "/patient/approved" });
					}
				}
			} catch (e) {
				enqueueSnackbar("Failed to submit Discharge Letter", {
					variant: "error",
				});
				console.error("Error submitting discharge letter:", e);
			} finally {
				setIsLoading(false);
			}
		}
	};

	const handleNext = async () => {
		setIsLoading(true);
		let selectedStatus = "";

		if (status === 1) {
			selectedStatus = "Provide Later";
		}

		if (status === 2) {
			selectedStatus = "No Active Prescription";
		}

		try {
			await axios.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/discharge-letter/status`,
				{ status: selectedStatus },
				{ withCredentials: true }
			);
			enqueueSnackbar("Status Updated", {
				variant: "success",
			});
			if (user == null) {
				enqueueSnackbar("No user connected found", {
					variant: "error",
				});
			} else {
				navigate({ to: "/patient/approved" });
			}
		} catch (e) {
			enqueueSnackbar("Failed to submit Discharge Letter", {
				variant: "error",
			});
			throw e;
		} finally {
			setIsLoading(false);
		}
	};
	const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSelectedFileName(e?.target?.files?.[0].name);
		setSelectedFile(e?.target?.files?.[0]);
	};
	return (
		<>
			{isLoading && <LoadingScreen />}
			<ThemeProvider theme={zenithTheme}>
				<Dialog
					open={openSuccessDialog}
					fullWidth={true}
					maxWidth={"xs"}
					onClose={() => setOpenSuccessDialog(false)}
				>
					<DialogContent>
						<Grid
							container
							direction={"column"}
							sx={{ width: "100%" }}
							justifyContent={"center"}
							alignItems={"center"}
						>
							<Typography sx={{ fontSize: "18px", fontWeight: "bold", mb: 3 }} align="center">
								Discharge Letter Submitted Successfully
							</Typography>
							<CheckCircleIcon sx={{ width: "100px", height: "100px", color: "green", mb: 2 }} />
							<Typography sx={{ fontSize: "14px" }} align="center">
								Thank you for submitting your discharge letter. Our team will review it shortly.
							</Typography>
							<Grid sx={{ mt: 2 }}>
								<Button
									variant="contained"
									color="primary"
									onClick={() => {
										setOpenSuccessDialog(false);
										navigate({ to: "/patient/introduce-harvest" });
									}}
								>
									Close
								</Button>
							</Grid>
						</Grid>
					</DialogContent>
				</Dialog>
				<Stack gap={2}>
					<Grid container direction={"column"} padding={"20px 0"}>
						<Grid size={12}>
							<Typography color="black" fontWeight="bold" variant="h5">
								Pre-Screening
							</Typography>
						</Grid>
						<Grid size={12}>
							<Typography sx={{ color: "green" }} fontWeight="bold" variant="h3">
								Upload Your Discharge Letter
							</Typography>
						</Grid>
					</Grid>

					<Typography sx={{ fontSize: "14px" }} gutterBottom>
						If you have an active prescription with another alternative medicine provider, we require their
						discharge letter.
					</Typography>

					<Grid
						container
						sx={{
							backgroundColor: "green",
							borderRadius: 2,
							width: "100%",
							p: 2,
						}}
						direction={"column"}
					>
						<Grid container sx={{ width: "100%" }} direction={"column"} alignItems={"start"} spacing={1}>
							<Grid sx={{ width: "100%" }}>
								<Typography align="left" sx={{ fontSize: "18px", fontWeight: "bold", color: "white" }}>
									Please email them to request it.
								</Typography>
							</Grid>
							<Grid sx={{ width: "100%" }}>
								<Typography align="left" sx={{ color: "white", fontWeight: "light", fontSize: "14px" }}>
									You can add it below, or you can add it later.*
								</Typography>
							</Grid>
						</Grid>
						<Grid
							sx={{
								backgroundColor: "white",
								borderRadius: 3,
								p: 2,
								mt: 2,
								width: "100%",
							}}
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
						>
							<Typography sx={{ fontSize: "16px", color: "#696969", fontWeight: "700" }} align="left">
								Your doctor can see your prescriptions and you will not be able to access our shop if
								you have other active prescriptions.
							</Typography>
						</Grid>

						<Grid sx={{ mt: 2, width: "100%" }} container justifyContent={"start"} alignItems={"start"}>
							<Grid sx={{ width: "100%" }} container justifyContent={"start"} alignItems={"start"}>
								<FormControl>
									<RadioGroup
										defaultValue="upload"
										name="letter-radio-buttons-group"
										sx={{ textAlign: "left" }}
									>
										<FormControlLabel
											value="upload"
											sx={{ color: "white" }}
											control={
												<Radio
													sx={{
														"&.Mui-checked, &.MuiRadio-root": {
															color: "white",
															borderRadius: 1,
															backgroundColor: "white",
															width: "20px",
															height: "20px",
															m: 1,
														},
													}}
													checked={status === 0}
													onClick={(e) => radioHandler(0)}
													checkedIcon={
														<CheckIcon sx={{ color: "green", fontSize: "medium" }} />
													}
												/>
											}
											label="Yes, I'll upload it"
										/>

										{userDetails?.member_status != "9b - Approve Subject To Discharge" && (
											<>
												<FormControlLabel
													value="later"
													sx={{ color: "white" }}
													control={
														<Radio
															sx={{
																"&.Mui-checked, &.MuiRadio-root": {
																	color: "white",
																	borderRadius: 1,
																	backgroundColor: "white",
																	width: "20px",
																	height: "20px",
																	m: 1,
																},
															}}
															checkedIcon={
																<CheckIcon
																	sx={{ color: "green", fontSize: "medium" }}
																/>
															}
															checked={status === 1}
															onClick={(e) => radioHandler(1)}
														/>
													}
													label="Yes, I'll provide later"
												/>

												<FormControlLabel
													value="none"
													sx={{ color: "white", whiteSpace: "nowrap" }}
													control={
														<Radio
															sx={{
																"&.Mui-checked, &.MuiRadio-root": {
																	color: "white",
																	borderRadius: 1,
																	backgroundColor: "white",
																	width: "20px",
																	height: "20px",
																	m: 1,
																},
															}}
															checkedIcon={
																<CheckIcon
																	sx={{ color: "green", fontSize: "medium" }}
																/>
															}
															checked={status === 2}
															onClick={(e) => radioHandler(2)}
														/>
													}
													label="No, I don't have an active prescription"
												/>
											</>
										)}
									</RadioGroup>
								</FormControl>
							</Grid>
						</Grid>

						<Grid sx={{ width: "100%" }}>
							<Typography sx={{ fontSize: "12px", color: "white", mt: 3 }} align="left">
								Upload Discharge Letter
							</Typography>
						</Grid>

						<Grid sx={{ mt: 1, width: "100%" }} container>
							<Grid size={{ xs: 5 }} container>
								<Button
									disabled={status !== 0}
									component="label"
									role={undefined}
									variant="contained"
									sx={{
										backgroundColor: "white",
										color: "black",
										textTransform: "none",
										width: "100%",
										mr: 0.5,
									}}
								>
									Choose File
									<VisuallyHiddenInput
										type="file"
										accept=".pdf,image/png,image/jpg,image/jpeg,image/heic,image/heif,.doc,.docx"
										onChange={handleFileChange}
									/>
								</Button>
							</Grid>
							<Grid container sx={{ backgroundColor: "white", borderRadius: 1 }} size={{ xs: 7 }}>
								<Grid sx={{ p: 1 }}>
									<Typography sx={{ fontSize: "12px" }}>
										{selectedFileName
											? selectedFileName.length > 30
												? `${selectedFileName.substring(0, 25)}...`
												: selectedFileName
											: "no file selected"}
									</Typography>
								</Grid>
							</Grid>
						</Grid>
					</Grid>

					{status === 0 ? (
						<Button
							disabled={status === 0 && !selectedFile ? true : false}
							variant="contained"
							fullWidth
							onClick={handleSubmit}
						>
							Submit
						</Button>
					) : (
						<Button disabled={status === 0} variant="contained" fullWidth onClick={handleNext}>
							Next
						</Button>
					)}

					<Typography sx={{ fontSize: "12px", color: "grey", mt: 1 }} align="left">
						Any information you provide today is confidential and compliant with the Medical Board of
						Australia Good Medical Practice code, RACGP Standards of General Practice and our Medical
						Confidentiality Duty of Conduct for doctors in Australia, which means we protect your privacy
						and right to confidentiality.
					</Typography>
				</Stack>
			</ThemeProvider>
		</>
	);
}

export default FormDischarge;
