import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  ThemeProvider,
  <PERSON>ack,
  Alert,
  CircularProgress,
  Grid2 as Grid,
  Button,
  FormControl,
  FormLabel,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Select,
  MenuItem,
  Divider,
  Drawer,
  IconButton,
} from "@mui/material";
import { ArrowBack } from "@mui/icons-material";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import { useFlow } from "../../../hooks/flow-controller";
import axiosInstance from "../../../services/axios";
import DynamicQuestionnaire from "../common/DynamicQuestionnaire";
import Banner from "../layouts/Banner";
import zenithTheme from "../../../styles/zenith/theme";
import CachedImage from "../common/CachedImage";
import { getAvailableStrengthOptions } from "../../../utils/quantityIncreaseValidation";
import { TreatmentPlan } from "../../../types";

const QuantityIncreaseDynamic: React.FC = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useFlow();

  // Component state
  const [isLoading, setIsLoading] = useState(false);
  const [canProceed, setCanProceed] = useState(false);
  const [contactId, setContactId] = useState<string>("");
  const [currentStep, setCurrentStep] = useState(1);
  const [totalSteps, setTotalSteps] = useState(5);
  const [maxScore, setMaxScore] = useState(0);

  // Strength selection state
  const [treatmentPlan, setTreatmentPlan] = useState<TreatmentPlan | null>(null);
  const [availableOptions, setAvailableOptions] = useState<Array<{value: string, label: string, current: number, availableLevels: number[]}>>([]);
  const [selectedStrengths, setSelectedStrengths] = useState<string[]>([]);
  const [strengthSelections, setStrengthSelections] = useState<{[key: string]: {selected: boolean, requestedQuantity: number}}>({});
  const [showStrengthSelection, setShowStrengthSelection] = useState(true);

  // Mobile drawer state
  const [drawerOpen, setDrawerOpen] = useState(false);

  // Check eligibility on component mount
  useEffect(() => {
    const checkEligibility = async () => {
      if (!user?.email) {
        enqueueSnackbar("Please log in to access this questionnaire", {
          variant: "error",
          autoHideDuration: 6000,
          preventDuplicate: true
        });
        navigate({ to: "/patient/login" });
        return;
      }

      try {
        setIsLoading(true);
        
        // Fetch treatment plan data
        const treatmentResponse = await axiosInstance.get(
          `/zoho/v1.0/treatment-plan/by-email?email=${encodeURIComponent(user.email)}`
        );

        if (treatmentResponse.data?.success && treatmentResponse.data?.treatmentPlan) {
          const planData = treatmentResponse.data.treatmentPlan;
          setContactId(treatmentResponse.data.contactId || "");

          // Check if patient has an active treatment plan with quantities below maximum
          const thc22Quantity = parseInt(planData.totalAllowance?.thc22 || '0');
          const thc29Quantity = parseInt(planData.totalAllowance?.thc29 || '0');

          const has22ThcBelowMax = thc22Quantity > 0 && thc22Quantity < 84;
          const has29ThcBelowMax = thc29Quantity > 0 && thc29Quantity < 84;



          if (!has22ThcBelowMax && !has29ThcBelowMax) {
            enqueueSnackbar(
              "You are not eligible for quantity increase. You must have an active treatment plan with quantities below the maximum (84g).",
              { 
                variant: "error",
                autoHideDuration: 6000,
                preventDuplicate: true
              }
            );
            navigate({ to: "/patient/home" });
            return;
          }

          // Set up treatment plan and available options
          setTreatmentPlan(planData);
          const options = getAvailableStrengthOptions(planData);
          setAvailableOptions(options);

          // Initialize strength selections
          const initialSelections: {[key: string]: {selected: boolean, requestedQuantity: number}} = {};
          options.forEach(option => {
            initialSelections[option.value] = {
              selected: false,
              requestedQuantity: option.availableLevels[0] || 0
            };
          });
          setStrengthSelections(initialSelections);

          setCanProceed(true);
        } else {
          enqueueSnackbar("Unable to fetch treatment plan data", {
            variant: "error",
            autoHideDuration: 6000,
            preventDuplicate: true
          });
          navigate({ to: "/patient/home" });
        }
      } catch (error) {
        console.error("Error checking eligibility:", error);
        enqueueSnackbar("Error checking eligibility. Please try again.", {
          variant: "error",
          autoHideDuration: 6000,
          preventDuplicate: true
        });
        navigate({ to: "/patient/home" });
      } finally {
        setIsLoading(false);
      }
    };

    checkEligibility();
  }, [user?.email, navigate, enqueueSnackbar]);

  // Handle score changes from the dynamic questionnaire
  const handleScoreChange = (totalScore: number, maxScore: number, isEligible: boolean) => {
    // Score tracking for internal use
    setMaxScore(maxScore);
  };

  // Handle step changes for progress indicator
  const handleStepChange = (step: number, total: number) => {
    setCurrentStep(step);
    setTotalSteps(total);
  };

  // Handle strength selection change (checkbox)
  const handleStrengthSelectionChange = (strength: string, selected: boolean) => {
    setStrengthSelections(prev => ({
      ...prev,
      [strength]: {
        ...prev[strength],
        selected
      }
    }));

    // Update selected strengths array
    setSelectedStrengths(prev =>
      selected
        ? [...prev, strength]
        : prev.filter(s => s !== strength)
    );
  };

  // Handle requested quantity change for specific strength
  const handleRequestedQuantityChange = (strength: string, quantity: number) => {
    setStrengthSelections(prev => ({
      ...prev,
      [strength]: {
        ...prev[strength],
        requestedQuantity: quantity
      }
    }));
  };

  // Mobile drawer handlers
  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleLogout = () => {
    // Clear any stored authentication data
    localStorage.removeItem('authToken');
    sessionStorage.clear();

    // Navigate to login page
    navigate({ to: "/patient/login" });
  };

  // Handle questionnaire submission
  const handleSubmit = async (submissionData: any) => {
    try {
      setIsLoading(true);

      // Prepare strength requests
      const strengthRequests = selectedStrengths.map(strength => {
        const option = availableOptions.find(opt => opt.value === strength);
        const selection = strengthSelections[strength];
        return {
          strength,
          currentQuantity: option?.current || 0,
          requestedQuantity: selection?.requestedQuantity || 0,
          increaseAmount: (selection?.requestedQuantity || 0) - (option?.current || 0)
        };
      });

      // Transform dynamic questionnaire data to match original endpoint format
      const transformedData = {
        questionsAndAnswers: submissionData.responses,
        selectedStrengths,
        strengthRequests,
        totalScore: submissionData.totalScore,
        maxScore,
        isEligible: submissionData.isEligible,
        submittedAt: submissionData.submittedAt,
      };

      // Submit to the backend using the same endpoint as the original QuantityIncrease component
      const result = await axiosInstance.post(
        `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/quantity-increase-questionnaire`,
        transformedData,
        { withCredentials: true }
      );

      if (result.data) {
        // Show success message (same as original implementation)
        enqueueSnackbar("Your request to increase quantity is under review.", {
          variant: "success",
          autoHideDuration: 4000,
          preventDuplicate: true
        });

        // Navigate back to home page to show approval/rejection status
        navigate({ to: "/patient/home" });
      }
    } catch (error) {
      console.error("Error submitting questionnaire:", error);
      enqueueSnackbar("Failed to submit questionnaire. Please try again.", {
        variant: "error",
        autoHideDuration: 6000,
        preventDuplicate: true
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    navigate({ to: "/patient/home" });
  };

  if (isLoading) {
    return (
      <ThemeProvider theme={zenithTheme}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
          <CircularProgress />
        </Box>
      </ThemeProvider>
    );
  }

  if (!canProceed) {
    return (
      <ThemeProvider theme={zenithTheme}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
          <Alert severity="error">
            You are not eligible for this questionnaire.
          </Alert>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={zenithTheme}>
      <Box sx={{ backgroundColor: "#f5f5f5", minHeight: "100vh", display: "flex", flexDirection: "column" }}>
        {/* Custom Header */}
        <Box sx={{ width: "100%" }}>
          <Banner />

          {/* Nav Bar */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "0 20px",
              backgroundColor: "white",
              // Account for banner height + some spacing
              paddingBottom: "10px",
            }}
          >
            <IconButton aria-label="menu" sx={{ padding: "8px" }} onClick={handleDrawerToggle}>
              <MenuIcon />
            </IconButton>

            <CachedImage
              src="/zenith/zenith-logo.png"
              alt="Zenith Clinics"
              sx={{
                height: "19.14px",
              }}
            />
          </Box>
        </Box>

        {/* Mobile Drawer */}
        <Drawer
          anchor="top"
          open={drawerOpen}
          onClose={handleDrawerToggle}
          keepMounted={true}
          sx={{
            "& .MuiDrawer-paper": {
              width: "100%",
              maxWidth: "100%",
              boxSizing: "border-box",
              padding: "20px 0 0 0",
              height: "auto",
              maxHeight: "500px",
              overflowY: "auto",
              borderBottom: "1px solid #e0e0e0",
              top: { xs: "40px", sm: "48px" }, // Push down by banner height
              zIndex: 1299, // Just below the banner
            },
          }}
        >
          {/* Drawer Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 20px', marginBottom: '10px' }}>
            <CachedImage
              src="/zenith/zenith-logo.png"
              alt="Zenith Clinics"
              sx={{ height: '36px' }}
            />
            <IconButton onClick={handleDrawerToggle}>
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Drawer Content */}
          <Box sx={{ display: 'flex', flexDirection: 'column', padding: '0 20px' }}>
            <Button
              onClick={() => {
                navigate({ to: "/patient/home" });
                setDrawerOpen(false);
              }}
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none',
                borderBottom: '1px solid #f0f0f0'
              }}
            >
              Home
            </Button>
            <Button
              onClick={() => {
                window.open(import.meta.env.VITE_ZENITH_PRIVATE_SHOP_URL, '_blank');
                setDrawerOpen(false);
              }}
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none',
                borderBottom: '1px solid #f0f0f0'
              }}
            >
              Visit Private Shop
            </Button>
            <Button
              onClick={handleLogout}
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none'
              }}
            >
              Logout
            </Button>
          </Box>
        </Drawer>

        {/* Main Content */}
        <Box sx={{
          flex: 1,
          padding: { xs: "10px", sm: "20px" },
          maxWidth: "100%",
          overflow: "hidden"
        }} className="questionnaire-container">
          {/* Back Button - positioned at top left */}
          <Box sx={{ marginBottom: 2, display: "flex", justifyContent: "flex-start", maxWidth: "800px", margin: "0 auto 16px auto", width: "100%" }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={handleBack}
              sx={{
                color: "#217F00",
                borderColor: "#217F00",
                fontSize: "16px",
                fontWeight: "bold",
                textTransform: "none",
                padding: "8px 16px",
                "&:hover": {
                  backgroundColor: "rgba(33, 127, 0, 0.04)",
                  borderColor: "#217F00",
                },
              }}
            >
              Back to Home
            </Button>
          </Box>

          <Grid
            container
            direction="column"
            justifyContent="flex-start"
            spacing={2}
            sx={{
              maxWidth: "100%",
              overflow: "hidden",
              px: { xs: 0, sm: 2 }
            }}
          >

            {/* Progress Indicator */}
            <Grid size={12}>
              <Stack direction="row" alignItems="center" justifyContent="center">
                <Box
                  sx={{
                    width: "32px",
                    borderRadius: "15px 0 0 15px",
                    height: "11px",
                    backgroundColor: currentStep >= 1 ? "green" : "#EAEAEA",
                    border: "0.5px solid rgba(89, 89, 89, 0.61)",
                  }}
                />
                {[...Array(totalSteps - 2)].map((_, index) => (
                  <Box
                    key={index}
                    sx={{
                      width: "32px",
                      height: "11px",
                      backgroundColor: currentStep > index + 1 ? "green" : "#EAEAEA",
                      border: "0.5px solid rgba(89, 89, 89, 0.61)",
                    }}
                  />
                ))}
                <Box
                  sx={{
                    width: "32px",
                    borderRadius: "0 15px 15px 0",
                    height: "11px",
                    backgroundColor: currentStep >= totalSteps ? "green" : "#EAEAEA",
                    border: "0.5px solid rgba(89, 89, 89, 0.61)",
                  }}
                />
              </Stack>
            </Grid>
            <Grid size={12}>
              <Stack sx={{ textAlign: "left", width: "100%" }}>
                <Typography variant="h4" fontWeight={800}>
                  Questionnaire:
                </Typography>
                <Typography variant="h5" fontWeight={700} color="green">
                  Request Quantity Increase
                </Typography>
              </Stack>
            </Grid>

            {/* Strength and Quantity Selection Section */}
            {showStrengthSelection && availableOptions.length > 0 && (
              <Grid size={12}>
                <Box sx={{
                  mb: 4,
                  p: { xs: 2, sm: 3 },
                  border: "2px solid #e0e0e0",
                  borderRadius: 2,
                  backgroundColor: "#f9f9f9",
                  maxWidth: "100%",
                  overflow: "hidden"
                }}>
                  <FormControl fullWidth>
                    <FormLabel sx={{
                      color: "black",
                      fontWeight: "bold",
                      mb: 2,
                      fontSize: { xs: "1rem", sm: "1.1rem" },
                      wordWrap: "break-word"
                    }}>
                      Which strength(s) would you like to increase? (Select all that apply)
                    </FormLabel>
                    <FormGroup>
                      {availableOptions.map((option) => (
                        <Box key={option.value} sx={{
                          mb: 3,
                          p: { xs: 1.5, sm: 2 },
                          border: "1px solid #ddd",
                          borderRadius: 2,
                          backgroundColor: "white",
                          maxWidth: "100%",
                          overflow: "hidden"
                        }}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={strengthSelections[option.value]?.selected || false}
                                onChange={(e) => handleStrengthSelectionChange(option.value, e.target.checked)}
                                sx={{
                                  color: "green",
                                  '&.Mui-checked': { color: "green" },
                                  alignSelf: "flex-start",
                                  mt: 0.5
                                }}
                              />
                            }
                            label={
                              <Box sx={{ maxWidth: "100%", overflow: "hidden" }}>
                                <Typography variant="body1" sx={{
                                  fontWeight: "bold",
                                  fontSize: { xs: "0.9rem", sm: "1rem" }
                                }}>
                                  {option.value}% THC
                                </Typography>
                                <Typography variant="body2" sx={{
                                  color: "#666",
                                  fontSize: { xs: "0.8rem", sm: "0.875rem" },
                                  wordWrap: "break-word",
                                  overflowWrap: "break-word"
                                }}>
                                  Current: {option.current}g/month → Up to {Math.max(...option.availableLevels)}g
                                </Typography>
                              </Box>
                            }
                            sx={{
                              alignItems: 'flex-start',
                              width: "100%",
                              '.MuiFormControlLabel-label': {
                                mt: 0.5,
                                textAlign: 'left',
                                display: 'block',
                                width: "100%",
                                overflow: "hidden"
                              }
                            }}
                          />

                          {/* Quantity Selection for this strength */}
                          {strengthSelections[option.value]?.selected && (
                            <Box sx={{
                              mt: 2,
                              ml: { xs: 2, sm: 4 },
                              maxWidth: "100%",
                              overflow: "hidden"
                            }}>
                              <FormLabel sx={{
                                color: "black",
                                fontWeight: "bold",
                                mb: 1,
                                display: "block",
                                fontSize: { xs: "0.85rem", sm: "0.875rem" },
                                wordWrap: "break-word"
                              }}>
                                Select new quantity for {option.value}% THC:
                              </FormLabel>
                              <Select
                                value={strengthSelections[option.value]?.requestedQuantity || option.availableLevels[0]}
                                onChange={(e) => handleRequestedQuantityChange(option.value, Number(e.target.value))}
                                fullWidth
                                size="small"
                                sx={{
                                  backgroundColor: "white",
                                  maxWidth: "100%",
                                  '& .MuiSelect-select': {
                                    fontSize: { xs: "0.8rem", sm: "0.875rem" }
                                  }
                                }}
                              >
                                {option.availableLevels.map((level) => (
                                  <MenuItem
                                    key={level}
                                    value={level}
                                    sx={{
                                      fontSize: { xs: "0.8rem", sm: "0.875rem" },
                                      whiteSpace: "normal",
                                      wordWrap: "break-word"
                                    }}
                                  >
                                    {level}g/month (+{level - option.current}g)
                                  </MenuItem>
                                ))}
                              </Select>
                            </Box>
                          )}
                        </Box>
                      ))}
                    </FormGroup>
                  </FormControl>

                  {/* Show selected increase details */}
                  {selectedStrengths.length > 0 && (
                    <Alert
                      severity="info"
                      sx={{
                        mt: 3,
                        maxWidth: "100%",
                        overflow: "hidden",
                        '& .MuiAlert-message': {
                          width: "100%",
                          overflow: "hidden"
                        }
                      }}
                    >
                      <Typography variant="body2" sx={{
                        fontWeight: "bold",
                        mb: 1,
                        fontSize: { xs: "0.8rem", sm: "0.875rem" }
                      }}>
                        Requesting increases:
                      </Typography>
                      {selectedStrengths.map(strength => {
                        const option = availableOptions.find(opt => opt.value === strength);
                        const selection = strengthSelections[strength];
                        const currentQty = option?.current || 0;
                        const requestedQty = selection?.requestedQuantity || 0;
                        return (
                          <Typography
                            key={strength}
                            variant="body2"
                            sx={{
                              ml: 1,
                              fontSize: { xs: "0.75rem", sm: "0.875rem" },
                              wordWrap: "break-word",
                              overflowWrap: "break-word",
                              lineHeight: 1.4
                            }}
                          >
                            • {strength}% THC: {currentQty}g → {requestedQty}g (+{requestedQty - currentQty}g)
                          </Typography>
                        );
                      })}
                    </Alert>
                  )}

                  <Divider sx={{ my: 3, borderColor: "#ccc" }} />

                  <Box sx={{
                    display: "flex",
                    justifyContent: "center",
                    mt: 2,
                    px: { xs: 1, sm: 0 }
                  }}>
                    <Button
                      variant="contained"
                      onClick={() => setShowStrengthSelection(false)}
                      disabled={selectedStrengths.length === 0}
                      sx={{
                        backgroundColor: "green",
                        color: "white",
                        fontWeight: "bold",
                        px: { xs: 3, sm: 4 },
                        py: 1,
                        fontSize: { xs: "0.85rem", sm: "0.875rem" },
                        minWidth: { xs: "200px", sm: "auto" },
                        maxWidth: "100%",
                        '&:hover': { backgroundColor: "#006400" },
                        '&:disabled': { backgroundColor: "#ccc" }
                      }}
                    >
                      Continue to Questionnaire
                    </Button>
                  </Box>
                </Box>
              </Grid>
            )}

            {/* Dynamic Questionnaire */}
            {!showStrengthSelection && (
              <>
                {/* Back to Selection Button */}
                <Grid size={12}>
                  <Box sx={{
                    mb: 2,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: { xs: "flex-start", sm: "center" },
                    flexDirection: { xs: "column", sm: "row" },
                    gap: { xs: 2, sm: 0 }
                  }}>
                    <Button
                      variant="outlined"
                      onClick={() => setShowStrengthSelection(true)}
                      sx={{
                        color: "green",
                        borderColor: "green",
                        fontWeight: "bold",
                        fontSize: { xs: "0.8rem", sm: "0.875rem" },
                        px: { xs: 2, sm: 3 },
                        '&:hover': { borderColor: "#006400", backgroundColor: "#f0f8f0" }
                      }}
                    >
                      ← Back to Strength Selection
                    </Button>

                    {/* Show current selections */}
                    <Box sx={{
                      textAlign: { xs: "left", sm: "right" },
                      maxWidth: "100%",
                      overflow: "hidden"
                    }}>
                      <Typography variant="body2" sx={{
                        fontWeight: "bold",
                        color: "green",
                        fontSize: { xs: "0.8rem", sm: "0.875rem" },
                        wordWrap: "break-word",
                        overflowWrap: "break-word"
                      }}>
                        Selected: {selectedStrengths.map(s => `${s}% THC`).join(', ')}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <DynamicQuestionnaire
                  questionnaireType="quantity_increase"
                  patientId={contactId}
                  onSubmit={handleSubmit}
                  onScoreChange={handleScoreChange}
                  onStepChange={handleStepChange}
                />
              </>
            )}
          </Grid>
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default QuantityIncreaseDynamic;
