import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Text<PERSON>ield, Typography, ThemeProvider, useMediaQuery, Box } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { AuthUser } from "../../../types";
import LoadingScreen from "../../../utils/loading-screen";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import axiosInstance from "../../../services/axios";
import { useFlow } from "../../../hooks/flow-controller";
import DontOfferDialog from "../dialogs/DontOfferDialog";

const inputStyle = {
	backgroundColor: "white",
	borderRadius: "72px",
	"& .MuiOutlinedInput-root": {
		borderRadius: "72px",
		"&:hover": {
			borderColor: "black",
		},
		"&.Mui-focused fieldset": {
			borderColor: "black",
			borderRadius: "72px",
		},
	},
	"& .MuiInputLabel-root": {
		color: "#3B3B3B",
	},
	"& .MuiInputLabel-root.Mui-focused": {
		color: "white",
	},
};

function Login() {
	const [canSubmit, setCanSubmit] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const { user, setUser, flowController } = useFlow();
	const navigate = useNavigate();
	const urlParams = new URLSearchParams(location.search); // Utilisez ceci si vous utilisez React Router
	const resetpassword = urlParams.get("resetpassword");
	const isDesktopOrMobile = useMediaQuery(zenithTheme.breakpoints.up("sm"));
	const { enqueueSnackbar } = useSnackbar();
	const [credentials, setCredentials] = useState({
		email: "",
		password: "",
	});

	// Get the return_to parameter from URL
	const getReturnPath = () => {
		const params = new URLSearchParams(window.location.search);
		return params.get("return_to");
	};

	// Simplified initial effect to prevent loops
	useEffect(() => {
		setIsLoading(false);
	}, []);
	useEffect(() => {
		if (resetpassword == "true") {
			enqueueSnackbar("Your password has been changed successfully, you can login with your new access", {
				variant: "success",
			});
		}
	}, [resetpassword]);
	useEffect(() => {
		setCanSubmit(credentials.email != "" && credentials.password != "");
	}, [credentials]);
	async function handleSubmit() {
		if (canSubmit) {
			setIsLoading(true);
			try {
				// First, let's try to clear any existing cookies that might be interfering
				document.cookie.split(";").forEach((cookie) => {
					const [name] = cookie.trim().split("=");
					if (name && !["__stripe_mid", "__stripe_sid"].includes(name)) {
						document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;`;
					}
				});

				// Now perform the login
				const result = await axiosInstance.post(
					`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/login/email-password`,
					credentials,
					{
						withCredentials: true,
						headers: {
							"Content-Type": "application/json",
							Accept: "application/json",
						},
					}
				);

				if (result.data && result.data.authenticated) {
					// Store authentication in localStorage as a backup
					localStorage.setItem("zenith_auth_user", JSON.stringify(result.data.user));
					localStorage.setItem("zenith_authenticated", "true");

					enqueueSnackbar("You have successfully logged in.", {
						variant: "success",
					});
					setUser(result.data?.user as AuthUser);
					// Get return path from URL parameters
					const returnPath = getReturnPath();

					// Special handling for consent form
					if (returnPath === "/patient/consent") {
						try {
							// Get user email from context or result
							const email = (result.data?.user?.email || user?.email) as string | undefined;
							if (!email) {
								enqueueSnackbar("Could not determine your email for consent form redirection.", {
									variant: "error",
								});
								return;
							}
							// Fetch contactId from Zoho by email
							const zohoUrl = `/zoho/v1.0/contacts/by-email?email=${encodeURIComponent(email)}`;
							const contactResp = await axiosInstance.get(zohoUrl);
							if (contactResp.data && contactResp.data.success && contactResp.data.id) {
								const contactId = contactResp.data.id;
								// Clear redirect key
								localStorage.removeItem("redirect_after_login");
								// Redirect to consent form with contactId
								const consentUrl = `/patient/consent/${contactId}`;
								window.location.href = consentUrl;
								return;
							} else {
								enqueueSnackbar("Could not find your consent form link. Please contact support.", {
									variant: "error",
								});
								return;
							}
						} catch (err) {
							enqueueSnackbar("Could not look up your consent form. Please contact support.", {
								variant: "error",
							});
							return;
						}
					}
					// Special handling for chat routes
					else if (returnPath && returnPath.startsWith("/patient/chat")) {
						try {
							// Get user email from context or result
							const email = (result.data?.user?.email || user?.email) as string | undefined;
							if (!email) {
								enqueueSnackbar("Could not determine your email for chat redirection.", {
									variant: "error",
								});
								return;
							}
							// Fetch contactId from Zoho by email (same as Home.tsx logic)
							const treatmentResponse = await axiosInstance.get(
								`/zoho/v1.0/treatment-plan/by-email?email=${encodeURIComponent(email)}`
							);
							if (treatmentResponse.data?.success && treatmentResponse.data?.contactId) {
								const contactId = treatmentResponse.data.contactId;
								// Redirect to chat with the correct contactId token
								const chatUrl = `/patient/chat?token=${encodeURIComponent(contactId)}`;
								window.location.href = chatUrl;
								return;
							} else {
								enqueueSnackbar("Could not find your chat information. Please contact support.", {
									variant: "error",
								});
								return;
							}
						} catch (err) {
							enqueueSnackbar("Could not look up your chat information. Please contact support.", {
								variant: "error",
							});
							return;
						}
					}
					// Special handling for schedule routes
					else if (returnPath === "/patient/schedule" || returnPath === "/schedule") {
						window.location.href = returnPath;
						return;
					}
					// Handle other return paths
					else if (returnPath) {
						window.location.href = returnPath;
						return;
					}

					// If no return path, use flow controller
					flowController(result.data?.user as AuthUser);
				} else {
					enqueueSnackbar("Authentication failed. Try again", {
						variant: "error",
					});
				}
			} catch (e: any) {
				console.log("Login - Error:", e);
				enqueueSnackbar("Authentication failed. Try again", {
					variant: "error",
				});
			} finally {
				setIsLoading(false);
			}
		} else {
			enqueueSnackbar("Please, fill the form", {
				variant: "error",
			});
		}
	}

	useEffect(() => {
		window.scrollTo(0, 0);
	}, []);

	return (
		<>
			{isLoading && <LoadingScreen />}
			<ThemeProvider theme={zenithTheme}>
				<Stack gap={1} marginBottom={"300px"} sx={{ marginBottom: "250px", width: "100%", padding: "20px" }}>
					<Grid container direction={"column"} padding={"20px 0"}>
						<Grid size={12}>
							<Typography
								sx={{
									fontSize: "38px",
									fontWeight: "bold",
									lineHeight: "1em",
									color: "green",
								}}
							>
								Login
							</Typography>
						</Grid>
					</Grid>
					<Grid size={12}>
						<Typography
							variant="h5"
							align="center"
							sx={{
								color: "black",
							}}
						>
							<span style={{ fontWeight: "600", color: "green" }}>Welcome,</span> please enter your
							account information.
						</Typography>
					</Grid>
					<Grid
						direction={"column"}
						justifyContent={"center"}
						container
						padding={!isDesktopOrMobile ? "20px 20px" : "50px"}
					>
						<Grid size={12} container direction={"column"} alignItems={"start"}>
							<Grid size={12}>
								<Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
									Email or phone number*
								</Typography>
							</Grid>
							<Grid size={12}>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="email"
									name="email"
									placeholder="Enter your email or phone number"
									size="small"
									onChange={(e) => setCredentials({ ...credentials, email: e.target.value })}
									margin="normal"
									fullWidth
									value={credentials.email}
									required={true}
									onKeyPress={(event) => {
										if (event.key === " ") {
											event.preventDefault();
										}
									}}
								/>
							</Grid>
						</Grid>
						<Grid size={12} container direction={"column"} alignItems={"start"} paddingTop={"10px"}>
							<Grid size={12}>
								<Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
									Password*
								</Typography>
							</Grid>
							<Grid size={12}>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="password"
									name="password"
									placeholder="Enter your password"
									size="small"
									onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
									margin="normal"
									fullWidth
									value={credentials.password}
									required={true}
								/>
							</Grid>
						</Grid>
						<Grid size={12} mt={2}>
							<Button
								type="submit"
								fullWidth
								disabled={!canSubmit}
								variant="contained"
								sx={{ mt: 1 }}
								onClick={handleSubmit}
							>
								Login
							</Button>
						</Grid>
						<Grid size={12} mt={2}>
							<Button
								sx={{
									textTransform: "none",
									color: "#0000EE",
									fontSize: "12px",
								}}
								onClick={() => navigate({ to: "/patient/forgot-password" })}
							>
								<span style={{ textDecoration: "underline", color: "green" }}>Forgot password?</span>
							</Button>
						</Grid>
						<Grid size={12}>
							<Button
								sx={{
									textTransform: "none",
									color: "black",
									fontSize: "12px",
								}}
								onClick={() => navigate({ to: "/patient/register" })}
							>
								New here?{" "}
								<span style={{ textDecoration: "underline", color: "green" }}>Register Now</span>
							</Button>
						</Grid>
					</Grid>
				</Stack>
			</ThemeProvider>
		</>
	);
}

export default Login;
