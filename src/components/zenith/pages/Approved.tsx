import { Box, Button, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useFlow } from "../../../hooks/flow-controller";
import axios from "axios";

const Approved = () => {
	const { user } = useFlow();

	const goToConsultation = () => {
		// Navigate to the consultation page or open a dialog
		axios
			.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/encrypt-id`, { user }, { withCredentials: true })
			.then((response) => {
				const encryptedLeadID = response.data.data.leadID;
				const baseUrl = `${import.meta.env.VITE_DRUI_URL}/schedule`;
				const url = `${baseUrl}?token=${encryptedLeadID}`;
				window.location.href = url;
			});
	};

	return (
		<Grid
			container
			direction="column"
			sx={{ width: "100%" }}
			justifyContent="center"
			alignItems="center"
			textAlign="center"
			spacing={2}
		>
			<Grid size={12}>
				<Typography color="green" variant="h3" fontWeight={"bold"}>
					<span style={{ color: "#515151" }}>You've Been</span> Approved
				</Typography>
			</Grid>
			<Grid size={12}>
				<Box
					sx={{
						backgroundColor: "green",
						borderRadius: "50%",
						width: 120,
						height: 120,
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						margin: "0 auto",
					}}
				>
					<img
						src="/zenith/check.png"
						alt="Approved"
						style={{ width: "80px", height: "80px", objectFit: "contain" }}
					/>
				</Box>
			</Grid>
			<Grid
				size={12}
				sx={{ width: "100%", fontSize: "14px", margin: "20px 0px" }}
				textAlign="start"
				container
				direction="column"
				alignItems="center"
			>
				<Typography variant="body1" align="center">
					Based on your responses to the questionnaire, you&apos;re eligible for a free consultation with one
					of our doctors.
				</Typography>
			</Grid>
			<Grid size={12}>
				<Typography variant="body1" align="center">
					This is a great opportunity to explore personalised treatment options tailored to your needs.
				</Typography>
			</Grid>
			<Grid size={12}>
				<Button
					fullWidth
					variant="contained"
					disableElevation
					sx={{ mt: 2, maxWidth: "320px", backgroundColor: "green", textTransform: "capitalize" }}
					onClick={goToConsultation}
				>
					Book Consultation
				</Button>
			</Grid>
		</Grid>
	);
};

export default Approved;
