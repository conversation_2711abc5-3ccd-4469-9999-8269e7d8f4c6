import { Button, <PERSON>ack, Typography, ThemeProvider } from "@mui/material";
import Grid from "@mui/material/Grid2";
import zenithTheme from "../../../styles/zenith/theme";

function Review() {
	return (
		<>
			<ThemeProvider theme={zenithTheme}>
				<Stack gap={1} marginBottom={"300px"} sx={{ marginBottom: "250px", width: "100%" }}>
					<Grid container direction={"column"} padding={"20px 0"} sx={{ borderRadius: "10px" }}>
						<Grid>
							<Typography
								sx={{
									fontSize: "45px",
									fontWeight: "bold",
									lineHeight: "1em",
								}}
							>
								Write Us
							</Typography>
							<Typography
								sx={{
									fontSize: "32px",
									fontWeight: "bold",
									lineHeight: "1em",
									color: "green",
								}}
							>
								A Google Review
							</Typography>
						</Grid>
					</Grid>
					<Grid textAlign="justify" marginTop={"50px"} marginBottom={"30px"}>
						<Typography
							sx={{
								fontSize: "26px",
								fontWeight: "bold",
								lineHeight: "1em",
								textAlign: "center",
								color: "green",
							}}
						>
							You’re all booked in!
						</Typography>
						<Typography
							sx={{
								fontSize: "20px",
								lineHeight: "1em",
								textAlign: "center",
								color: "black",
							}}
						>
							We look forward to seeing you
						</Typography>
					</Grid>
					<Grid textAlign="justify" alignSelf={"center"} width={"70%"} marginBottom={"30px"}>
						<Typography
							sx={{
								fontSize: "18px",
								lineHeight: "1em",
								textAlign: "center",
								color: "black",
							}}
						>
							We'd apprecciate it if you could add a review to our <b>Google Page</b>.
						</Typography>
						<Typography
							sx={{
								marginTop: "20px",
								fontSize: "18px",
								lineHeight: "1em",
								textAlign: "center",
								color: "black",
							}}
						>
							This will help others to know what to expect from the process.
						</Typography>
					</Grid>
					<Grid>
						<a target="blank" href="https://g.page/r/CWVb4fu4o1XUEAI/review">
							<Button type="submit" fullWidth variant="contained" sx={{ mt: 2 }}>
								Write a Review
							</Button>
						</a>
					</Grid>
					<Grid textAlign="justify" alignSelf={"center"} width={"30%"} marginTop={"30px"}>
						<img src="/zenith/google-review.png" width={"100%"} />
					</Grid>
				</Stack>
			</ThemeProvider>
		</>
	);
}

export default Review;
