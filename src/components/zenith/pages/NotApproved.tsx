import { Button, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";

const NotApproved = () => {
	const handleBackToHomepage = () => {
		window.location.href = "/";
	};

	return (
		<Grid
			container
			direction="column"
			sx={{ width: "100%" }}
			justifyContent="center"
			alignItems="center"
			textAlign="center"
			spacing={2}
		>
			<Typography
				sx={{
					fontSize: "24px",
					fontWeight: "bold",
					lineHeight: "1em",
				}}
			>
				You Have Not Been Approved
			</Typography>
			<img src="/zenith/cancel.png" alt="Not Approved" style={{ width: "200px", height: "auto" }} />
			<Grid
				size={12}
				sx={{ width: "100%", fontSize: "14px", margin: "20px 0px" }}
				textAlign="start"
				container
				direction="column"
				alignItems="center"
			>
				<Typography variant="body1" align="center">
					Based on your responses, you do not currently meet the eligibility criteria for a free doctor
					consultation.
				</Typography>
			</Grid>
			<Grid size={12}>
				<Typography variant="body1" align="center">
					We appreciate your time and interest in our clinic, and we wish you the very best on your health
					journey.
				</Typography>
			</Grid>
			<Grid size={12}>
				<Button
					fullWidth
					variant="contained"
					disableElevation
					sx={{ mt: 2, maxWidth: "320px", backgroundColor: "green", textTransform: "capitalize" }}
					onClick={handleBackToHomepage}
				>
					Back to Homepage
				</Button>
			</Grid>
		</Grid>
	);
};

export default NotApproved;
