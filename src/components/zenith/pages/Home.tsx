import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>P<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Badge } from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import InstagramIcon from "@mui/icons-material/Instagram";
import zenithTheme from "../../../styles/zenith/theme";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import { useFlow } from "../../../hooks/flow-controller";
import LoadingScreen from "../../../utils/loading-screen";
import axiosInstance from "../../../services/axios";
import Banner from "../layouts/Banner";
import CachedImage from "../common/CachedImage";
import DenialDialog from "../dialogs/DenialDialog";
import WaitingPeriodModal from "../modals/WaitingPeriodModal";
import ExtendTPWaitingPeriodModal from "../modals/ExtendTPWaitingPeriodModal";
import {
	getWaitingPeriodStatus,
	getExtendTPWaitingPeriodStatus,
	shouldDelayRejectionStatus,
} from "../../../utils/waitingPeriod";
import { isEligibleForAdd22ThcQuestionnaire, hasOnly22ThcTreatmentPlan } from "../../../utils/treatmentPlanValidation";
import { isEligibleForQuantityIncrease } from "../../../utils/quantityIncreaseValidation";
import { QuantityIncreaseQuestionnaireStatus } from "../../../types/quantityIncrease";
import { tryEmailAuthentication } from "../../../services/email-auth";

// Helper function to determine if rejection status should be shown
// Returns true if rejection should be shown, false if it should be delayed (show pending instead)
function shouldShowRejectionStatus(questionnaireStatus: any, currentDate: Date = new Date()): boolean {
	// For explicitly rejected status, show rejection after delay period
	if (questionnaireStatus?.status === "rejected") {
		// If we don't have submission date, show rejection (fallback)
		if (!questionnaireStatus?.submittedAt) {
			return true;
		}

		// Check if one business day has passed since submission
		// shouldDelayRejectionStatus returns true if we should delay (show pending)
		// We want the opposite - show rejection if delay period has ended
		return !shouldDelayRejectionStatus(questionnaireStatus.submittedAt, currentDate);
	}

	// For non-eligible users with submitted status, show rejection after delay period
	if (questionnaireStatus?.status === "submitted" && questionnaireStatus?.isEligible === false) {
		// If we don't have submission date, show rejection (fallback)
		if (!questionnaireStatus?.submittedAt) {
			return true;
		}

		// Check if one business day has passed since submission
		return !shouldDelayRejectionStatus(questionnaireStatus.submittedAt, currentDate);
	}

	// For all other cases, don't show rejection
	return false;
}

// Helper function to determine effective status for display
// This overrides 'rejected' status or non-eligible 'submitted' status with 'under_review' if rejection should be delayed
function getEffectiveQuestionnaireStatus(questionnaireStatus: any): string {
	if (!questionnaireStatus?.status) {
		return "not_started";
	}

	// If it's rejected but we should delay showing rejection, show as under_review
	if (questionnaireStatus.status === "rejected" && !shouldShowRejectionStatus(questionnaireStatus)) {
		return "under_review";
	}

	// If it's a non-eligible submitted questionnaire but we should delay showing rejection, show as under_review
	if (
		questionnaireStatus.status === "submitted" &&
		questionnaireStatus.isEligible === false &&
		!shouldShowRejectionStatus(questionnaireStatus)
	) {
		return "under_review";
	}

	return questionnaireStatus.status;
}

// Helper function to check if any questionnaire has been rejected
// Returns true if any questionnaire is completed and should show rejection status
function hasAnyQuestionnaireRejection(
	thcStatus: any,
	extendTPStatus: any,
	add22ThcStatus: any,
	quantityIncreaseStatus: any
): boolean {
	const questionnaires = [thcStatus, extendTPStatus, add22ThcStatus, quantityIncreaseStatus];

	return questionnaires.some(questionnaire =>
		questionnaire?.completed && shouldShowRejectionStatus(questionnaire)
	);
}

function Home() {
	const [isLoading, setIsLoading] = useState(false);
	const [contactId, setContactId] = useState<string | null>(null);
	const [drawerOpen, setDrawerOpen] = useState(false);
	const [patientName, setPatientName] = useState<string>("");
	const [thcQuestionnaireStatus, setThcQuestionnaireStatus] = useState<{
		completed: boolean;
		score: number;
		isEligible: boolean;
		status?: string;
		submittedAt?: string;
	} | null>(null);
	const [extendTPQuestionnaireStatus, setExtendTPQuestionnaireStatus] = useState<{
		completed: boolean;
		score: number;
		isEligible: boolean;
		status?: string;
		submittedAt?: string;
	} | null>(null);
	const [add22ThcQuestionnaireStatus, setAdd22ThcQuestionnaireStatus] = useState<{
		completed: boolean;
		score: number;
		isEligible: boolean;
		status?: string;
		submittedAt?: string;
	} | null>(null);
	const [quantityIncreaseQuestionnaireStatus, setQuantityIncreaseQuestionnaireStatus] =
		useState<QuantityIncreaseQuestionnaireStatus | null>(null);
	const [showDenialModal, setShowDenialModal] = useState(false);
	const [denialType, setDenialType] = useState<"thc" | "extend" | "add22thc" | null>(null);
	const [showWaitingPeriodModal, setShowWaitingPeriodModal] = useState(false);
	const [treatmentPlanStartDate, setTreatmentPlanStartDate] = useState<string>("");
	const [isInWaitingPeriod, setIsInWaitingPeriod] = useState(false);
	const [showExtendTPWaitingPeriodModal, setShowExtendTPWaitingPeriodModal] = useState(false);
	const [treatmentPlanEndDate, setTreatmentPlanEndDate] = useState<string>("");
	const [isInExtendTPWaitingPeriod, setIsInExtendTPWaitingPeriod] = useState(false);
	const [chatNotificationStatus, setChatNotificationStatus] = useState<boolean | null>(null);
	const [hasPendingChatNotifications, setHasPendingChatNotifications] = useState<boolean>(false);
	const [showViewBookingButton, setShowViewBookingButton] = useState<boolean>(false);
	const [treatmentPlan, setTreatmentPlan] = useState<any>(null);
	const { user } = useFlow();
	const navigate = useNavigate();
	const { enqueueSnackbar } = useSnackbar();

	useEffect(() => {
		const fetchPatientData = async (skipEmailAuth = false) => {
			if (!user?.email) {
				return;
			}

			try {
				setIsLoading(true);

				// First, try to authenticate with email-only if we have user data but might be missing tokens
				let authenticationAttempted = false;
				if (!skipEmailAuth) {
					try {
						const authResult = await tryEmailAuthentication();
						if (authResult.success) {
							console.log("Email authentication successful");
							authenticationAttempted = true;
						}
					} catch (authError) {
						console.log("Email authentication not needed or failed:", authError);
					}
				}

				// Fetch treatment plan data
				const treatmentResponse = await axiosInstance.get(
					`/zoho/v1.0/treatment-plan/by-email?email=${encodeURIComponent(user.email)}`
				);

				if (treatmentResponse.data?.success && treatmentResponse.data?.contactId) {
					setContactId(treatmentResponse.data.contactId);
					setShowViewBookingButton(false); // Hide view booking button when we have contact info

					// Store treatment plan data for eligibility checks
					if (treatmentResponse.data?.treatmentPlan) {
						setTreatmentPlan(treatmentResponse.data.treatmentPlan);
					}

					// Fetch chat notification status using contactId as patientId
					try {
						const chatNotificationResponse = await axiosInstance.get(
							`/chat/v1.0/notifications/${user.email}`
						);

						if (chatNotificationResponse.data?.success && chatNotificationResponse.data?.data) {
							const hasNotifications = chatNotificationResponse.data.data.chatNotification === true;
							// Set chat notification status - only show "Update Treatment Plan" when chatNotification is false
							setChatNotificationStatus(!hasNotifications); // Show button when no notifications
							setHasPendingChatNotifications(hasNotifications); // Show badge when there are notifications
						} else {
							// If no notification data found, default to showing the button (chatNotification = false)
							setChatNotificationStatus(true);
							setHasPendingChatNotifications(false);
						}
					} catch (chatError) {
						console.error("Error fetching chat notifications:", chatError);
						// On error, default to showing the button (treat as chatNotification = false)
						setChatNotificationStatus(true);
						setHasPendingChatNotifications(false);
					}

					// Extract patient name from user data
					if (user?.fullName) {
						setPatientName(user.fullName);
					} else if (user?.firstname) {
						setPatientName(user.firstname);
					}

					// Extract treatment plan start date and calculate waiting period status
					if (treatmentResponse.data?.treatmentPlan?.treatmentPlanStartDate) {
						const startDate = treatmentResponse.data.treatmentPlan.treatmentPlanStartDate;
						setTreatmentPlanStartDate(startDate);

						// Calculate waiting period status
						const waitingStatus = getWaitingPeriodStatus(startDate);
						setIsInWaitingPeriod(waitingStatus.isInWaitingPeriod);
					}

					// Extract treatment plan end date and calculate extend TP waiting period status
					if (treatmentResponse.data?.treatmentPlan?.treatmentPlanEndDate) {
						const endDate = treatmentResponse.data.treatmentPlan.treatmentPlanEndDate;
						setTreatmentPlanEndDate(endDate);

						// Calculate extend TP waiting period status
						const extendWaitingStatus = getExtendTPWaitingPeriodStatus(endDate);
						setIsInExtendTPWaitingPeriod(extendWaitingStatus.isInWaitingPeriod);
					}
				}

				// Fetch THC questionnaire status
				try {
					const thcResponse = await axiosInstance.get(
						`/funnel/v1.0/patient/thc-increase-questionnaire/status?email=${encodeURIComponent(
							user.email
						)}`,
						{ withCredentials: true }
					);

					if (thcResponse.data?.success && thcResponse.data?.questionnaire) {
						const questionnaire = thcResponse.data.questionnaire;
						setThcQuestionnaireStatus({
							completed: true,
							score: questionnaire.totalScore || 0,
							isEligible: questionnaire.isEligible || false,
							status: questionnaire.status,
							submittedAt: questionnaire.submittedAt,
						});

						// Don't auto-show modal for rejected status - only show when button is clicked
					}
				} catch (thcError) {
					// THC questionnaire not completed or error fetching - this is expected for many users
					console.log("THC questionnaire not found or error:", thcError);
					setThcQuestionnaireStatus({
						completed: false,
						score: 0,
						isEligible: false,
					});
				}

				// Fetch ExtendTP questionnaire status
				try {
					const extendTPResponse = await axiosInstance.get(
						`/funnel/v1.0/patient/extend-tp-questionnaire/status?email=${encodeURIComponent(user.email)}`,
						{ withCredentials: true }
					);

					if (extendTPResponse.data?.success && extendTPResponse.data?.questionnaire) {
						const questionnaire = extendTPResponse.data.questionnaire;
						setExtendTPQuestionnaireStatus({
							completed: true,
							score: questionnaire.totalScore || 0,
							isEligible: questionnaire.isEligible || false,
							status: questionnaire.status,
							submittedAt: questionnaire.submittedAt,
						});

						// Don't auto-show modal for rejected status - only show when button is clicked
					}
				} catch (extendTPError) {
					// ExtendTP questionnaire not completed or error fetching - this is expected for many users
					console.log("ExtendTP questionnaire not found or error:", extendTPError);
					setExtendTPQuestionnaireStatus({
						completed: false,
						score: 0,
						isEligible: false,
					});
				}

				// Fetch Add 22% THC questionnaire status
				try {
					const add22ThcResponse = await axiosInstance.get(
						`/funnel/v1.0/patient/add-22-thc-questionnaire/status?email=${encodeURIComponent(user.email)}`,
						{ withCredentials: true }
					);

					if (add22ThcResponse.data?.success && add22ThcResponse.data?.questionnaire) {
						const questionnaire = add22ThcResponse.data.questionnaire;
						setAdd22ThcQuestionnaireStatus({
							completed: true,
							score: questionnaire.totalScore || 0,
							isEligible: questionnaire.isEligible || false,
							status: questionnaire.status,
							submittedAt: questionnaire.submittedAt,
						});
					}
				} catch (add22ThcError) {
					// Add 22% THC questionnaire not completed or error fetching - this is expected for many users
					console.log("Add 22% THC questionnaire not found or error:", add22ThcError);
					setAdd22ThcQuestionnaireStatus({
						completed: false,
						score: 0,
						isEligible: false,
					});
				}

				// Fetch quantity increase questionnaire status
				try {
					const quantityIncreaseResponse = await axiosInstance.get(
						`/funnel/v1.0/patient/quantity-increase-questionnaire/status?email=${encodeURIComponent(
							user.email
						)}`,
						{ withCredentials: true }
					);

					console.log("Quantity Increase API Response:", quantityIncreaseResponse.data);

					if (quantityIncreaseResponse.data?.success && quantityIncreaseResponse.data?.questionnaire) {
						const questionnaire = quantityIncreaseResponse.data.questionnaire;
						const statusData = {
							completed: true,
							score: questionnaire.totalScore || 0,
							isEligible: questionnaire.isEligible || false,
							status: questionnaire.status,
							submittedAt: questionnaire.submittedAt,
							selectedStrengths: questionnaire.selectedStrengths,
							strengthRequests: questionnaire.strengthRequests,
						};
						console.log("Setting Quantity Increase Status:", statusData);
						console.log("Effective Status:", getEffectiveQuestionnaireStatus(statusData));
						console.log("Should Show Rejection:", shouldShowRejectionStatus(statusData));
						setQuantityIncreaseQuestionnaireStatus(statusData);
					}
				} catch (quantityIncreaseError) {
					// Quantity increase questionnaire not completed or error fetching - this is expected for many users
					console.log("Quantity increase questionnaire not found or error:", quantityIncreaseError);
					setQuantityIncreaseQuestionnaireStatus({
						completed: false,
						score: 0,
						isEligible: false,
						status: "not_started",
					});
				}
			} catch (error: any) {
				console.error("Error fetching patient data:", error);

				// Check if the error is a 401/403 (authentication error) and we haven't tried email auth yet
				if ((error?.response?.status === 401 || error?.response?.status === 403) && !skipEmailAuth) {
					console.log("Authentication error detected, trying email authentication...");
					try {
						const authResult = await tryEmailAuthentication();
						if (authResult.success) {
							console.log("Email authentication successful, retrying data fetch...");
							// Retry the data fetch after successful authentication
							fetchPatientData(true); // Skip email auth on retry
							return;
						}
					} catch (authError) {
						console.error("Email authentication failed:", authError);
					}
				}

				// Check if the error is a 404 (contact information not found)
				if (error?.response?.status === 404) {
					console.log("Contact information not found (404), showing View Booking button");
					setShowViewBookingButton(true);

					// Set patient name from user data when we don't have treatment plan data
					if (user?.fullName) {
						setPatientName(user.fullName);
					} else if (user?.firstname) {
						setPatientName(user.firstname);
					}
				}
			} finally {
				setIsLoading(false);
			}
		};

		fetchPatientData();
	}, [user?.email, user?.firstname]);

	const navigateToShop = () => {
		if (user?.email && contactId) {
			const safeContactId = contactId.startsWith("p") ? contactId : `p${contactId}`;
			const shopUrl = `https://letsroll.harvest.delivery/members-shop?email=${encodeURIComponent(
				user.email
			)}&contact=${encodeURIComponent(safeContactId)}`;
			window.location.href = shopUrl;
		} else {
			enqueueSnackbar("Unable to access shop. Please contact support.", {
				variant: "error",
			});
		}
	};

	const toggleDrawer = () => {
		setDrawerOpen(!drawerOpen);
	};

	const handleLogout = () => {
		// Clear authentication data
		localStorage.removeItem("zenith_auth_user");
		localStorage.removeItem("zenith_authenticated");

		// Close drawer
		toggleDrawer();

		// Redirect to login page
		navigate({ to: "/patient/login" });
	};

	const handleThcIncreaseClick = () => {
		// Check if user is in waiting period
		if (isInWaitingPeriod) {
			setShowWaitingPeriodModal(true);
		} else {
			// Navigate to dynamic THC increase questionnaire page
			navigate({ to: `/patient/increase-dynamic` });
		}
	};

	const handleExtendTPClick = () => {
		// Check if user is in waiting period for extend TP
		if (isInExtendTPWaitingPeriod) {
			setShowExtendTPWaitingPeriodModal(true);
		} else {
			// Navigate to dynamic extend TP questionnaire page
			navigate({ to: "/patient/extend-dynamic" });
		}
	};

	const handleAdd22ThcClick = () => {
		// Navigate to dynamic add 22% THC questionnaire page
		navigate({ to: "/patient/add-22-thc-dynamic" });
	};

	const handleQuantityIncreaseClick = () => {
		// Navigate to dynamic quantity increase questionnaire page
		navigate({ to: "/patient/quantity-increase-dynamic" });
	};

	// Helper function to format strength requests for display
	const formatStrengthRequests = (
		strengthRequests?: Array<{
			strength: string;
			currentQuantity: number;
			requestedQuantity: number;
			increaseAmount: number;
		}>
	) => {
		if (!strengthRequests || strengthRequests.length === 0) return "% THC (g → g)";

		if (strengthRequests.length === 1) {
			const request = strengthRequests[0];
			return `${request.strength}% THC (${request.currentQuantity}g → ${request.requestedQuantity}g)`;
		}

		// Multiple strengths
		const formattedRequests = strengthRequests.map(
			(request) => `${request.strength}% THC (${request.currentQuantity}g → ${request.requestedQuantity}g)`
		);
		return formattedRequests.join(" and ");
	};

	// Helper function to format strength names for rejection message
	const formatStrengthNames = (selectedStrengths?: string[]) => {
		if (!selectedStrengths || selectedStrengths.length === 0) return "% THC";

		if (selectedStrengths.length === 1) {
			return `${selectedStrengths[0]}% THC`;
		}

		return `${selectedStrengths.join("% and ")}% THC`;
	};

	if (isLoading) {
		return <LoadingScreen />;
	}

	return (
		<ThemeProvider theme={zenithTheme}>
			<Box
				sx={{
					display: "flex",
					flexDirection: "column",
					minHeight: "100vh",
					backgroundColor: "white",
				}}
			>
				{/* Custom Header */}
				<Box sx={{ width: "100%" }}>
					<Banner />

					{/* Nav Bar */}
					<Box
						sx={{
							display: "flex",
							justifyContent: "space-between",
							alignItems: "center",
							padding: "0 20px",
							backgroundColor: "white",
							paddingTop: { xs: "50px", sm: "58px" }, // Account for banner height + some spacing
							paddingBottom: "10px",
						}}
					>
						<IconButton aria-label="menu" sx={{ padding: "8px" }} onClick={toggleDrawer}>
							<Badge
								variant="dot"
								color="error"
								invisible={!hasPendingChatNotifications} // Show badge when there are pending notifications
								sx={{
									"& .MuiBadge-badge": {
										backgroundColor: "#ff4444",
										width: "10px",
										height: "10px",
										minWidth: "10px",
										borderRadius: "50%",
										right: "2px",
										top: "1px",
									},
								}}
							>
								<MenuIcon />
							</Badge>
						</IconButton>

						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{
								height: "19.14px",
							}}
						/>
					</Box>
				</Box>

				{/* Navigation Drawer */}
				<Drawer
					anchor="top"
					open={drawerOpen}
					onClose={toggleDrawer}
					keepMounted={true}
					sx={{
						"& .MuiDrawer-paper": {
							width: "100%",
							maxWidth: "100%",
							boxSizing: "border-box",
							padding: "20px 0 0 0",
							height: "auto",
							maxHeight: "500px",
							overflowY: "auto",
							borderBottom: "1px solid #e0e0e0",
							top: { xs: "40px", sm: "48px" }, // Push down by banner height
							zIndex: 1299, // Just below the banner
						},
					}}
				>
					{/* Drawer Header */}
					<Box
						sx={{
							display: "flex",
							justifyContent: "space-between",
							alignItems: "center",
							padding: "0 20px",
							marginBottom: "10px",
						}}
					>
						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{ height: "36px", objectFit: "contain" }}
						/>
						<IconButton onClick={toggleDrawer}>
							<CloseIcon />
						</IconButton>
					</Box>

					{/* Divider */}
					<Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

					{/* Navigation Links */}
					<Box sx={{ padding: "0 20px 20px" }}>
						{!showViewBookingButton && (
							<Button
								sx={{
									color: "#217F00",
									fontSize: "18px",
									fontWeight: "bold",
									justifyContent: "flex-start",
									padding: "15px 0",
									width: "100%",
									textTransform: "none",
									borderBottom: "1px solid #f0f0f0",
								}}
								onClick={() => {
									toggleDrawer();
									navigate({ to: "/patient/profile" });
								}}
							>
								View Treatment Plan
							</Button>
						)}
						{/* Only show Update Treatment Plan when chatNotification is false */}
						{!showViewBookingButton && hasPendingChatNotifications === true && (
							<Button
								sx={{
									color: "#217F00",
									fontSize: "18px",
									fontWeight: "bold",
									justifyContent: "flex-start",
									padding: "15px 0",
									width: "100%",
									textTransform: "none",
									borderBottom: "1px solid #f0f0f0",
									position: "relative",
								}}
								onClick={() => {
									toggleDrawer();
									if (contactId) {
										navigate({
											to: `/patient/chat?token=${encodeURIComponent(contactId)}`,
										});
									} else {
										enqueueSnackbar("Unable to access chat. Please contact support.", {
											variant: "error",
										});
									}
								}}
							>
								<Badge
									variant="dot"
									color="error"
									invisible={!hasPendingChatNotifications} // Show badge when there are pending notifications
									sx={{
										"& .MuiBadge-badge": {
											backgroundColor: "#ff4444",
											width: "8px",
											height: "8px",
											minWidth: "8px",
											borderRadius: "50%",
											right: "-8px",
											top: "12px",
										},
									}}
								>
									View Chat
								</Badge>
							</Button>
						)}
						{(showViewBookingButton ||
							hasAnyQuestionnaireRejection(
								thcQuestionnaireStatus,
								extendTPQuestionnaireStatus,
								add22ThcQuestionnaireStatus,
								quantityIncreaseQuestionnaireStatus
							)) && (
							<Button
								sx={{
									color: "#217F00",
									fontSize: "18px",
									fontWeight: "bold",
									justifyContent: "flex-start",
									padding: "15px 0",
									width: "100%",
									textTransform: "none",
									borderBottom: "1px solid #f0f0f0",
								}}
								onClick={() => {
									toggleDrawer();
									navigate({ to: "/patient/bookings" });
								}}
							>
								View Booking
							</Button>
						)}
						{!showViewBookingButton && user?.email !== "<EMAIL>" ? (
							<Button
								sx={{
									color: "#217F00",
									fontSize: "18px",
									fontWeight: "bold",
									justifyContent: "flex-start",
									padding: "15px 0",
									width: "100%",
									textTransform: "none",
									borderBottom: "1px solid #f0f0f0",
								}}
								onClick={() => {
									toggleDrawer();
									navigateToShop();
								}}
							>
								Visit Private Shop
							</Button>
						) : null}
						<Button
							sx={{
								color: "#217F00",
								fontSize: "18px",
								fontWeight: "bold",
								justifyContent: "flex-start",
								padding: "15px 0",
								width: "100%",
								textTransform: "none",
							}}
							onClick={handleLogout}
						>
							Logout
						</Button>
					</Box>
				</Drawer>

				{/* Main Content */}
				<Box
					sx={{
						flexGrow: 1,
						display: "flex",
						flexDirection: "column",
						padding: "20px",
						backgroundColor: "white",
					}}
				>
					{/* Center Logo */}
					<Box
						sx={{
							display: "flex",
							justifyContent: "center",
							marginBottom: "30px",
							marginTop: "20px",
						}}
					>
						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{
								width: "241px",
								height: "31px",
								margin: "0 auto",
							}}
						/>
					</Box>

					{/* Welcome Message */}
					<Typography
						sx={{
							fontWeight: "bold",
							textAlign: "center",
							fontSize: "18px",
							marginBottom: "20px",
							color: "#333",
						}}
					>
						Welcome {patientName || "(patient name)"}
					</Typography>

					{/* Intro Text */}
					<Typography
						sx={{
							textAlign: "center",
							marginBottom: "35px",
							fontSize: "16px",
							lineHeight: "21px",
							padding: "0 15px",
							color: "#333",
						}}
					>
						You’ve taken the first step toward real, personalised care. Everything you need to stay on your
						path to natural care is just a click away.
					</Typography>

					{/* THC Increase Approval Notification */}
					{thcQuestionnaireStatus?.completed &&
						thcQuestionnaireStatus?.isEligible &&
						thcQuestionnaireStatus?.status === "approved" && (
							<Box
								sx={{
									backgroundColor: "#f8f9fa",
									border: "1px solid #d1d5db",
									borderRadius: "8px",
									padding: "16px",
									margin: "0 16px 24px 16px",
									position: "relative",
								}}
							>
								<Box
									sx={{
										position: "absolute",
										top: "8px",
										right: "12px",
										fontSize: "18px",
										fontWeight: "bold",
										color: "#6b7280",
										cursor: "pointer",
									}}
									onClick={() => {
										setThcQuestionnaireStatus((prev) =>
											prev ? { ...prev, status: "dismissed" } : null
										);
									}}
								>
									×
								</Box>
								<Typography
									sx={{
										fontSize: "14px",
										color: "#374151",
										marginBottom: "4px",
										paddingRight: "32px", // Add padding to avoid overlap with close button
									}}
								>
									Your request to increase to 29% THC has been{" "}
									<Typography
										component="span"
										sx={{
											color: "#16a34a",
											fontWeight: "bold",
										}}
									>
										approved
									</Typography>
									. Your updated treatment plan is now active and ready to use.
								</Typography>
							</Box>
						)}

					{/* Treatment Plan Extension Approval Notification */}
					{extendTPQuestionnaireStatus?.completed &&
						extendTPQuestionnaireStatus?.isEligible &&
						extendTPQuestionnaireStatus?.status === "approved" && (
							<Box
								sx={{
									backgroundColor: "#f8f9fa",
									border: "1px solid #d1d5db",
									borderRadius: "8px",
									padding: "16px",
									margin: "0 16px 24px 16px",
									position: "relative",
								}}
							>
								<Box
									sx={{
										position: "absolute",
										top: "8px",
										right: "12px",
										fontSize: "18px",
										fontWeight: "bold",
										color: "#6b7280",
										cursor: "pointer",
									}}
									onClick={() => {
										setExtendTPQuestionnaireStatus((prev) =>
											prev ? { ...prev, status: "dismissed" } : null
										);
									}}
								>
									×
								</Box>
								<Typography
									sx={{
										fontSize: "14px",
										color: "#374151",
										marginBottom: "4px",
										paddingRight: "32px", // Add padding to avoid overlap with close button
									}}
								>
									Your treatment plan extension has been{" "}
									<Typography
										component="span"
										sx={{
											color: "#16a34a",
											fontWeight: "bold",
										}}
									>
										approved
									</Typography>
									. Your updated plan is now active and ready to go.
								</Typography>
							</Box>
						)}

					{/* Add 22% THC Approval Notification */}
					{add22ThcQuestionnaireStatus?.completed &&
						add22ThcQuestionnaireStatus?.isEligible &&
						add22ThcQuestionnaireStatus?.status === "approved" && (
							<Box
								sx={{
									backgroundColor: "#f8f9fa",
									border: "1px solid #d1d5db",
									borderRadius: "8px",
									padding: "16px",
									margin: "0 16px 24px 16px",
									position: "relative",
								}}
							>
								<Box
									sx={{
										position: "absolute",
										top: "8px",
										right: "12px",
										fontSize: "18px",
										fontWeight: "bold",
										color: "#6b7280",
										cursor: "pointer",
									}}
									onClick={() => {
										setAdd22ThcQuestionnaireStatus((prev) =>
											prev ? { ...prev, status: "dismissed" } : null
										);
									}}
								>
									×
								</Box>
								<Typography
									sx={{
										fontSize: "14px",
										color: "#374151",
										marginBottom: "4px",
										paddingRight: "32px", // Add padding to avoid overlap with close button
									}}
								>
									Your request to add 22% THC option has been{" "}
									<Typography
										component="span"
										sx={{
											color: "#16a34a",
											fontWeight: "bold",
										}}
									>
										approved
									</Typography>
									. Your updated treatment plan now includes both 22% and 29% THC options.
								</Typography>
							</Box>
						)}

					{/* Quantity Increase Approval Notification */}
					{quantityIncreaseQuestionnaireStatus?.completed &&
						quantityIncreaseQuestionnaireStatus?.isEligible &&
						quantityIncreaseQuestionnaireStatus?.status === "approved" && (
							<Box
								sx={{
									backgroundColor: "#f8f9fa",
									border: "1px solid #d1d5db",
									borderRadius: "8px",
									padding: "16px",
									margin: "0 16px 24px 16px",
									position: "relative",
								}}
							>
								<Box
									sx={{
										position: "absolute",
										top: "8px",
										right: "8px",
										width: "20px",
										height: "20px",
										borderRadius: "50%",
										backgroundColor: "#6b7280",
										color: "white",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										fontSize: "12px",
										fontWeight: "bold",
										cursor: "pointer",
									}}
									onClick={() => {
										setQuantityIncreaseQuestionnaireStatus((prev) =>
											prev ? { ...prev, status: "dismissed" } : null
										);
									}}
								>
									×
								</Box>
								<Typography
									sx={{
										fontSize: "14px",
										color: "#374151",
										marginBottom: "4px",
										paddingRight: "32px", // Add padding to avoid overlap with close button
										lineHeight: 1.4,
									}}
								>
									Your quantity increase request for{" "}
									<Typography
										component="span"
										sx={{
											fontWeight: "bold",
										}}
									>
										{formatStrengthRequests(quantityIncreaseQuestionnaireStatus?.strengthRequests)}
									</Typography>{" "}
									has been{" "}
									<Typography
										component="span"
										sx={{
											color: "#16a34a",
											fontWeight: "bold",
										}}
									>
										approved
									</Typography>
									. Your updated treatment plan is now active.
								</Typography>
							</Box>
						)}

					{/* Action Buttons */}
					<Stack
						spacing={0}
						sx={{
							marginBottom: "30px",
							px: 2,
							width: "100%",
							alignItems: "center",
							display: "flex",
							flexDirection: "column",
						}}
					>
						{!showViewBookingButton && (
							<Typography variant="body1" fontWeight="bold" align="center" sx={{ marginBottom: "24px" }}>
								Manage Your Treatment Plan
							</Typography>
						)}

						{/* Denial Banner Notifications */}
						{thcQuestionnaireStatus?.completed && shouldShowRejectionStatus(thcQuestionnaireStatus) && (
							<Box
								sx={{
									backgroundColor: "#ffffff",
									border: "1px solid #333333",
									borderRadius: "8px",
									padding: "12px 16px",
									margin: "16px 0",
									position: "relative",
									maxWidth: "400px",
									width: "100%",
								}}
							>
								<IconButton
									size="small"
									onClick={() => {
										setThcQuestionnaireStatus((prev) =>
											prev ? { ...prev, status: "dismissed" } : null
										);
									}}
									sx={{
										color: "#333333",
										position: "absolute",
										top: "8px",
										right: "8px",
										padding: "4px",
									}}
								>
									<CloseIcon fontSize="small" />
								</IconButton>
								<Typography
									sx={{
										fontSize: "14px",
										color: "#333333",
										paddingRight: "32px", // Add padding to avoid overlap with close button
									}}
								>
									Your THC increase{" "}
									<span style={{ color: "#ff6b6b", fontWeight: "bold" }}>wasn't approved</span>.
									Please reach out to the team for more information — we're here to help.
								</Typography>
							</Box>
						)}

						{extendTPQuestionnaireStatus?.completed &&
							shouldShowRejectionStatus(extendTPQuestionnaireStatus) && (
								<Box
									sx={{
										backgroundColor: "#ffffff",
										border: "1px solid #333333",
										borderRadius: "8px",
										padding: "12px 16px",
										margin: "16px 0",
										position: "relative",
										maxWidth: "400px",
										width: "100%",
									}}
								>
									<IconButton
										size="small"
										onClick={() => {
											setExtendTPQuestionnaireStatus((prev) =>
												prev ? { ...prev, status: "dismissed" } : null
											);
										}}
										sx={{
											color: "#333333",
											position: "absolute",
											top: "8px",
											right: "8px",
											padding: "4px",
										}}
									>
										<CloseIcon fontSize="small" />
									</IconButton>
									<Typography
										sx={{
											fontSize: "14px",
											color: "#333333",
											paddingRight: "32px", // Add padding to avoid overlap with close button
										}}
									>
										Your treatment plan extension{" "}
										<span style={{ color: "#ff6b6b", fontWeight: "bold" }}>wasn't approved</span>.
										Please reach out to the team for more information — we're here to help.
									</Typography>
								</Box>
							)}

						{add22ThcQuestionnaireStatus?.completed &&
							shouldShowRejectionStatus(add22ThcQuestionnaireStatus) && (
								<Box
									sx={{
										backgroundColor: "#ffffff",
										border: "1px solid #333333",
										borderRadius: "8px",
										padding: "12px 16px",
										margin: "16px 0",
										position: "relative",
										maxWidth: "400px",
										width: "100%",
									}}
								>
									<IconButton
										size="small"
										onClick={() => {
											setAdd22ThcQuestionnaireStatus((prev) =>
												prev ? { ...prev, status: "dismissed" } : null
											);
										}}
										sx={{
											color: "#333333",
											position: "absolute",
											top: "8px",
											right: "8px",
											padding: "4px",
										}}
									>
										<CloseIcon fontSize="small" />
									</IconButton>
									<Typography
										sx={{
											fontSize: "14px",
											color: "#333333",
											paddingRight: "32px", // Add padding to avoid overlap with close button
										}}
									>
										Your request to add 22% THC option{" "}
										<span style={{ color: "#ff6b6b", fontWeight: "bold" }}>wasn't approved</span>.
										Please reach out to the team for more information — we're here to help.
									</Typography>
								</Box>
							)}

						{/* Quantity Increase Rejection Notification */}
						{quantityIncreaseQuestionnaireStatus?.completed &&
							shouldShowRejectionStatus(quantityIncreaseQuestionnaireStatus) && (
								<Box
									sx={{
										backgroundColor: "#ffffff",
										border: "1px solid #333333",
										borderRadius: "8px",
										padding: "12px 16px",
										margin: "16px 0",
										position: "relative",
										maxWidth: "400px",
										width: "100%",
									}}
								>
									<IconButton
										size="small"
										onClick={() => {
											setQuantityIncreaseQuestionnaireStatus((prev) =>
												prev ? { ...prev, status: "dismissed" } : null
											);
										}}
										sx={{
											color: "#333333",
											position: "absolute",
											top: "8px",
											right: "8px",
											padding: "4px",
										}}
									>
										<CloseIcon fontSize="small" />
									</IconButton>
									<Typography
										sx={{
											fontSize: "14px",
											color: "#333333",
											paddingRight: "32px", // Add padding to avoid overlap with close button
										}}
									>
										Your quantity increase request for{" "}
										{formatStrengthNames(quantityIncreaseQuestionnaireStatus?.selectedStrengths)}{" "}
										<span style={{ color: "#ff6b6b", fontWeight: "bold" }}>wasn't approved</span>.
										Please reach out to the team for more information — we're here to help.
									</Typography>
								</Box>
							)}

						{/* Only show treatment-related buttons when we have contact information */}
						{/* THC Increase Button - Only show for patients with 22% THC treatment plan only */}
						{!showViewBookingButton &&
						treatmentPlan &&
						hasOnly22ThcTreatmentPlan(treatmentPlan) &&
						thcQuestionnaireStatus?.completed &&
						thcQuestionnaireStatus?.isEligible &&
						(getEffectiveQuestionnaireStatus(thcQuestionnaireStatus) === "submitted" ||
							getEffectiveQuestionnaireStatus(thcQuestionnaireStatus) === "under_review") ? (
							<Box
								sx={{
									backgroundColor: "#808080",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									cursor: "default",
									marginBottom: "24px",
									minHeight: "54px", // Fixed height to match normal buttons
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
								}}
							>
								<Box
									component="img"
									src="/zenith/info_icon.png"
									alt="Info"
									sx={{
										width: "20px",
										height: "20px",
										filter: "brightness(0) invert(1)", // Makes the icon white to match button text
										flexShrink: 0,
									}}
								/>
								<Box sx={{ textAlign: "center", lineHeight: 1.2 }}>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "16px", fontWeight: "bold" }}
									>
										Increase to a 29% THC Plan
									</Box>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "12px", opacity: 0.9, mt: 0.25 }}
									>
										(Pending Doctor's Review)
									</Box>
								</Box>
							</Box>
						) : !showViewBookingButton &&
						  treatmentPlan &&
						  hasOnly22ThcTreatmentPlan(treatmentPlan) &&
						  thcQuestionnaireStatus?.completed &&
						  shouldShowRejectionStatus(thcQuestionnaireStatus) ? (
							<Box
								sx={{
									backgroundColor: "#dc3545",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
									marginBottom: "24px",
								}}
								onClick={() => {
									setDenialType("thc");
									setShowDenialModal(true);
								}}
							>
								<Box
									component="img"
									src="/zenith/info_icon.png"
									alt="Info"
									sx={{
										width: "20px",
										height: "20px",
										filter: "brightness(0) invert(1)", // Makes the icon white to match button text
									}}
								/>
								Increase to a 29% THC Plan
							</Box>
						) : !showViewBookingButton &&
						  treatmentPlan &&
						  hasOnly22ThcTreatmentPlan(treatmentPlan) &&
						  isInWaitingPeriod ? (
							<Box
								sx={{
									backgroundColor: "#808080",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
									marginBottom: "24px",
									minHeight: "54px", // Fixed height to match normal buttons
								}}
								onClick={handleThcIncreaseClick}
							>
								<Box
									component="img"
									src="/zenith/info_icon.png"
									alt="Info"
									sx={{
										width: "20px",
										height: "20px",
										filter: "brightness(0) invert(1)", // Makes the icon white to match button text
										flexShrink: 0,
									}}
								/>
								<Box sx={{ textAlign: "center", lineHeight: 1.2 }}>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "16px", fontWeight: "bold" }}
									>
										Increase to a 29% Treatment Plan
									</Box>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "12px", opacity: 0.9, mt: 0.25 }}
									>
										(Available 1 month after treatment start)
									</Box>
								</Box>
							</Box>
						) : !showViewBookingButton && treatmentPlan && hasOnly22ThcTreatmentPlan(treatmentPlan) ? (
							<Box
								sx={{
									backgroundColor: "#217F00",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									marginBottom: "24px",
								}}
								onClick={handleThcIncreaseClick}
							>
								Increase to a 29% Treatment Plan
							</Box>
						) : null}

						{!showViewBookingButton &&
						extendTPQuestionnaireStatus?.completed &&
						extendTPQuestionnaireStatus?.isEligible &&
						(getEffectiveQuestionnaireStatus(extendTPQuestionnaireStatus) === "submitted" ||
							getEffectiveQuestionnaireStatus(extendTPQuestionnaireStatus) === "under_review") ? (
							<Box
								sx={{
									backgroundColor: "#808080",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									cursor: "default",
									marginBottom: "24px",
									minHeight: "54px", // Fixed height to match normal buttons
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
								}}
							>
								<Box
									component="img"
									src="/zenith/info_icon.png"
									alt="Info"
									sx={{
										width: "20px",
										height: "20px",
										filter: "brightness(0) invert(1)", // Makes the icon white to match button text
										flexShrink: 0,
									}}
								/>
								<Box sx={{ textAlign: "center", lineHeight: 1.2 }}>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "16px", fontWeight: "bold" }}
									>
										Extend Treatment Plan
									</Box>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "12px", opacity: 0.9, mt: 0.25 }}
									>
										(Pending Doctor's Review)
									</Box>
								</Box>
							</Box>
						) : !showViewBookingButton &&
						  extendTPQuestionnaireStatus?.completed &&
						  shouldShowRejectionStatus(extendTPQuestionnaireStatus) ? (
							<Box
								sx={{
									backgroundColor: "#dc3545",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
									marginBottom: "24px",
								}}
								onClick={() => {
									setDenialType("extend");
									setShowDenialModal(true);
								}}
							>
								<Box
									component="img"
									src="/zenith/info_icon.png"
									alt="Info"
									sx={{
										width: "20px",
										height: "20px",
										filter: "brightness(0) invert(1)", // Makes the icon white to match button text
									}}
								/>
								Extend Treatment Plan
							</Box>
						) : !showViewBookingButton && isInExtendTPWaitingPeriod ? (
							<Box
								sx={{
									backgroundColor: "#808080",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
									marginBottom: "24px",
									minHeight: "54px", // Fixed height to match normal buttons
								}}
								onClick={handleExtendTPClick}
							>
								<Box
									component="img"
									src="/zenith/info_icon.png"
									alt="Info"
									sx={{
										width: "20px",
										height: "20px",
										filter: "brightness(0) invert(1)", // Makes the icon white to match button text
										flexShrink: 0,
									}}
								/>
								<Box sx={{ textAlign: "center", lineHeight: 1.2 }}>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "16px", fontWeight: "bold" }}
									>
										Extend Treatment Plan
									</Box>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "12px", opacity: 0.9, mt: 0.25 }}
									>
										(Available 14 days before plan expires)
									</Box>
								</Box>
							</Box>
						) : !showViewBookingButton ? (
							<Box
								sx={{
									backgroundColor: "#217F00",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									marginBottom: "24px",
								}}
								onClick={handleExtendTPClick}
							>
								Extend Treatment Plan
							</Box>
						) : null}

						{/* Add 22% THC Option Button - Only show for patients with 29% THC treatment plan only */}
						{!showViewBookingButton &&
						treatmentPlan &&
						isEligibleForAdd22ThcQuestionnaire(treatmentPlan) &&
						add22ThcQuestionnaireStatus?.completed &&
						add22ThcQuestionnaireStatus?.isEligible &&
						(getEffectiveQuestionnaireStatus(add22ThcQuestionnaireStatus) === "submitted" ||
							getEffectiveQuestionnaireStatus(add22ThcQuestionnaireStatus) === "under_review") ? (
							<Box
								sx={{
									backgroundColor: "#808080",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									cursor: "default",
									marginBottom: "24px",
									minHeight: "54px", // Fixed height to match normal buttons
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
								}}
							>
								<Box
									component="img"
									src="/zenith/info_icon.png"
									alt="Info"
									sx={{
										width: "20px",
										height: "20px",
										filter: "brightness(0) invert(1)", // Makes the icon white to match button text
										flexShrink: 0,
									}}
								/>
								<Box sx={{ textAlign: "center", lineHeight: 1.2 }}>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "16px", fontWeight: "bold" }}
									>
										Add 22% THC Option
									</Box>
									<Box
										component="span"
										sx={{ display: "block", fontSize: "12px", opacity: 0.9, mt: 0.25 }}
									>
										(Pending Doctor's Review)
									</Box>
								</Box>
							</Box>
						) : !showViewBookingButton &&
						  treatmentPlan &&
						  isEligibleForAdd22ThcQuestionnaire(treatmentPlan) &&
						  add22ThcQuestionnaireStatus?.completed &&
						  shouldShowRejectionStatus(add22ThcQuestionnaireStatus) ? (
							<Box
								sx={{
									backgroundColor: "#dc3545",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
									marginBottom: "24px",
								}}
								onClick={() => {
									setDenialType("add22thc");
									setShowDenialModal(true);
								}}
							>
								<Box
									component="img"
									src="/zenith/info_icon.png"
									alt="Info"
									sx={{
										width: "20px",
										height: "20px",
										filter: "brightness(0) invert(1)", // Makes the icon white to match button text
									}}
								/>
								Add 22% THC Option
							</Box>
						) : !showViewBookingButton &&
						  treatmentPlan &&
						  isEligibleForAdd22ThcQuestionnaire(treatmentPlan) ? (
							<Box
								sx={{
									backgroundColor: "#217F00",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									marginBottom: "24px",
								}}
								onClick={handleAdd22ThcClick}
							>
								Add 22% THC Option
							</Box>
						) : null}

						{/* Quantity Increase Button - Only show for patients with existing quantities who can increase */}
						{!showViewBookingButton &&
						treatmentPlan &&
						isEligibleForQuantityIncrease(treatmentPlan) &&
						quantityIncreaseQuestionnaireStatus?.completed &&
						(getEffectiveQuestionnaireStatus(quantityIncreaseQuestionnaireStatus) === "submitted" ||
							getEffectiveQuestionnaireStatus(quantityIncreaseQuestionnaireStatus) === "under_review") ? (
							<Box
								sx={{
									backgroundColor: "#808080",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									marginBottom: "24px",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
								}}
							>
								<Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
									<img
										src="/zenith/info_icon.png"
										alt="Info"
										style={{
											width: "16px",
											height: "16px",
											filter: "brightness(0) invert(1)", // Makes the icon white to match button text
										}}
									/>
									<Box>
										<Box component="span" sx={{ display: "block" }}>
											Quantity Increase Request
										</Box>
										<Box
											component="span"
											sx={{ display: "block", fontSize: "12px", opacity: 0.9, mt: 0.25 }}
										>
											(Pending Doctor's Review)
										</Box>
									</Box>
								</Box>
							</Box>
						) : !showViewBookingButton &&
						  treatmentPlan &&
						  isEligibleForQuantityIncrease(treatmentPlan) &&
						  quantityIncreaseQuestionnaireStatus?.completed &&
						  shouldShowRejectionStatus(quantityIncreaseQuestionnaireStatus) ? (
							<Box
								sx={{
									backgroundColor: "#dc3545",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									marginBottom: "24px",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
								}}
								onClick={handleQuantityIncreaseClick}
							>
								<img
									src="/zenith/info_icon.png"
									alt="Info"
									style={{
										width: "16px",
										height: "16px",
										filter: "brightness(0) invert(1)", // Makes the icon white to match button text
									}}
								/>
								Request Quantity Increase
							</Box>
						) : !showViewBookingButton && treatmentPlan && isEligibleForQuantityIncrease(treatmentPlan) ? (
							<Box
								sx={{
									backgroundColor: "#217F00",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									marginBottom: "24px",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "8px",
								}}
								onClick={handleQuantityIncreaseClick}
							>
								Request Quantity Increase
							</Box>
						) : null}

						{!showViewBookingButton && (
							<Box
								sx={{
									border: "2px solid #000000",
									color: "black",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
								}}
								onClick={() => navigate({ to: "/patient/profile" })}
							>
								View Current Treatment Plan
							</Box>
						)}
						{(showViewBookingButton ||
							hasAnyQuestionnaireRejection(
								thcQuestionnaireStatus,
								extendTPQuestionnaireStatus,
								add22ThcQuestionnaireStatus,
								quantityIncreaseQuestionnaireStatus
							)) && (
							<Box
								sx={{
									backgroundColor: "#217F00",
									color: "white",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									maxWidth: "400px",
									width: "100%",
									marginTop: "20px",
								}}
								onClick={() => navigate({ to: "/patient/bookings" })}
							>
								View Booking
							</Box>
						)}
						{!showViewBookingButton && user?.email !== "<EMAIL>" ? (
							<Stack
								width={"100%"}
								spacing={2}
								alignContent={"center"}
								alignItems={"center"}
								sx={{ marginTop: "20px" }}
							>
								<Typography variant="body1" fontWeight={700} align="center" gutterBottom>
									Access Your Benefits
								</Typography>
								<Box
									sx={{
										backgroundColor: "#217F00",
										color: "white",
										padding: "15px 20px",
										borderRadius: "50px",
										textAlign: "center",
										fontWeight: "bold",
										cursor: "pointer",
										fontSize: "16px",
										width: "100%",
										maxWidth: "400px",
									}}
									onClick={navigateToShop}
								>
									Visit Our Private Shop
								</Box>
							</Stack>
						) : null}
						{/* <Stack width={"100%"} alignContent={"center"} alignItems={"center"} spacing={2}>
							<Typography variant="body1" fontWeight={700} align="center" gutterBottom>
								Appreciate Your Doctor's Care
							</Typography>
							<Box
								sx={{
									border: "2px solid #000000",
									color: "black",
									padding: "15px 20px",
									borderRadius: "50px",
									textAlign: "center",
									fontWeight: "bold",
									cursor: "pointer",
									fontSize: "16px",
									width: "100%",
									maxWidth: "400px",
								}}
								onClick={() => navigate({ to: "/patient/thankyou" })}
							>
								Send a Thank You Message
							</Box>
						</Stack> */}
						<Box
							sx={{
								display: "none",
								justifyContent: "start",
								flexDirection: "column",
								alignItems: "start",
								width: "100%",
							}}
						>
							<Typography
								sx={{
									color: "#007F00",
									fontSize: "24px",
									fontWeight: 600,
									textAlign: "start",
								}}
							>
								Disclaimers
							</Typography>
							<Box>
								<ol>
									<li style={{ textAlign: "start", marginBottom: "20px" }}>
										Our health professionals will evaluate your situation to determine if our
										services are suitable for you.{" "}
									</li>
									<li style={{ textAlign: "start", marginBottom: "20px" }}>
										Treatment outcomes may vary depending on individual circumstances.{" "}
									</li>
									<li style={{ textAlign: "start", marginBottom: "20px" }}>
										Appointment and consultation bookings are dependent on availability.
									</li>
									<li style={{ textAlign: "start", marginBottom: "20px" }}>
										Zenith Clinic does not promote or guarantee the use of alternative treatments
										for every individual. Responses to natural therapies can vary based on factors
										such as age, weight, health, dosage, and tolerance. These treatments may not be
										suitable for everyone and may cause side effects. Always seek medical advice
										from your doctor to determine what’s right for you. In Australia, alternative
										treatments are regulated by the Therapeutic Goods Administration (TGA). For more
										information, visit the TGA website.
									</li>
								</ol>
							</Box>
						</Box>
					</Stack>
				</Box>

				{/* Footer */}
				<Box sx={{ backgroundColor: "white" }}>
					{/* Footer Logo */}
					<Box
						sx={{
							display: "flex",
							justifyContent: "flex-start",
							marginBottom: "5px",
							padding: "0 20px",
						}}
					>
						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{
								width: "150px",
							}}
						/>
					</Box>

					{/* Divider */}
					<Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

					{/* Footer Links */}
					<Stack spacing={1} sx={{ padding: "0 20px 10px", alignItems: "flex-start" }}>
						<Typography
							sx={{
								fontWeight: "500",
								fontSize: "14px",
								cursor: "pointer",
							}}
							onClick={() => navigate({ to: "/patient/register" })}
						>
							Start Pre-Screening
						</Typography>
						<Typography
							sx={{
								fontWeight: "500",
								fontSize: "14px",
								cursor: "pointer",
							}}
							onClick={() => window.open("https://zenith.clinics/about", "_blank")}
						>
							About Us
						</Typography>
						<Typography
							sx={{
								fontWeight: "500",
								fontSize: "14px",
								cursor: "pointer",
							}}
							onClick={() => window.open("https://zenith.clinics/how-we-help", "_blank")}
						>
							How We Help
						</Typography>
						<Typography
							sx={{
								fontWeight: "500",
								fontSize: "14px",
								cursor: "pointer",
							}}
							onClick={() => window.open("https://zenith.clinics/pricing", "_blank")}
						>
							Pricing
						</Typography>
						<Typography
							sx={{
								fontWeight: "500",
								fontSize: "14px",
								cursor: "pointer",
							}}
							onClick={() => window.open("https://zenith.clinics/faqs", "_blank")}
						>
							FAQs
						</Typography>
					</Stack>

					{/* Divider */}
					<Box
						sx={{
							height: "1px",
							backgroundColor: "#e0e0e0",
							margin: "0 0 10px",
						}}
					/>

					{/* Trust Indicators */}
					<Box
						sx={{
							display: "flex",
							justifyContent: "space-around",
							padding: "10px 20px",
							marginBottom: "10px",
						}}
					>
						<Box
							sx={{
								display: "flex",
								flexDirection: "column",
								alignItems: "center",
								maxWidth: "45%",
							}}
						>
							<Box
								component="img"
								src="/zenith/customers-icon.svg"
								alt="Customers"
								sx={{ height: "40px", marginBottom: "5px" }}
							/>
							<Typography
								sx={{
									fontSize: "10.53px",
									textAlign: "center",
									fontWeight: "500",
									lineHeight: 1.2,
								}}
							>
								Over 10,000 Happy Customers
							</Typography>
						</Box>

						<Box
							sx={{
								display: "flex",
								flexDirection: "column",
								alignItems: "center",
								maxWidth: "45%",
							}}
						>
							<Box
								component="img"
								src="/zenith/secure-icon.svg"
								alt="Secure"
								sx={{ height: "40px", marginBottom: "5px" }}
							/>
							<Typography
								sx={{
									fontSize: "10.53px",
									textAlign: "center",
									fontWeight: "500",
									lineHeight: 1.2,
								}}
							>
								Safe, Secure & Easy Checkout
							</Typography>
						</Box>
					</Box>

					{/* Divider */}
					<Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

					{/* Social Links */}
					<Box
						sx={{
							display: "flex",
							justifyContent: "center",
							gap: "20px",
							padding: "10px 0",
						}}
					>
						<Box
							sx={{
								width: "36px",
								height: "36px",
								borderRadius: "50%",
								backgroundColor: "#217F00",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								color: "white",
								cursor: "pointer",
							}}
							onClick={() => window.open("https://twitter.com/zenithclinics", "_blank")}
						>
							{/* X Logo (Twitter) */}
							<Typography sx={{ fontWeight: "bold", fontSize: "16px", lineHeight: 1 }}>X</Typography>
						</Box>
						<Box
							sx={{
								width: "36px",
								height: "36px",
								borderRadius: "50%",
								backgroundColor: "#217F00",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								color: "white",
								cursor: "pointer",
							}}
							onClick={() => window.open("https://facebook.com/zenithclinics", "_blank")}
						>
							{/* Facebook f */}
							<Typography
								sx={{
									fontWeight: "bold",
									fontSize: "22px",
									lineHeight: 1,
									marginBottom: "2px",
								}}
							>
								f
							</Typography>
						</Box>
						<Box
							sx={{
								width: "36px",
								height: "36px",
								borderRadius: "50%",
								backgroundColor: "#217F00",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								color: "white",
								cursor: "pointer",
							}}
							onClick={() => window.open("https://instagram.com/zenithclinics", "_blank")}
						>
							<InstagramIcon sx={{ fontSize: "18px" }} />
						</Box>
					</Box>

					{/* Divider */}
					<Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

					{/* Contact Info - Updated to single line */}
					<Box
						sx={{
							padding: "10px 20px",
							display: "flex",
							justifyContent: "space-between",
							alignItems: "center",
						}}
					>
						<Box sx={{ display: "flex", alignItems: "center", gap: "10px" }}>
							<Box component="img" src="/zenith/email-icon.svg" alt="Email" sx={{ height: "20px" }} />
							<Typography sx={{ fontSize: "14px" }}><EMAIL></Typography>
						</Box>

						<Box sx={{ display: "flex", alignItems: "center", gap: "10px" }}>
							<Box component="img" src="/zenith/phone-icon.svg" alt="Phone" sx={{ height: "20px" }} />
							<Typography sx={{ fontSize: "14px" }}>(02) 7228 8399</Typography>
						</Box>
					</Box>

					{/* Divider */}
					<Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

					{/* Company Info */}
					<Box
						sx={{
							padding: "10px 20px",
							textAlign: "center",
							marginBottom: "20px",
						}}
					>
						<Typography sx={{ fontSize: "14px" }}>
							Provided by <span style={{ color: "#217F00", fontWeight: "bold" }}>ZenithClinics</span> Pty
							Ltd
						</Typography>
					</Box>
				</Box>
			</Box>

			{/* Denial Dialog */}
			{denialType && (
				<DenialDialog
					open={showDenialModal}
					onClose={() => {
						setShowDenialModal(false);
						setDenialType(null);
					}}
					type={denialType}
				/>
			)}

			{/* Waiting Period Modal */}
			<WaitingPeriodModal
				open={showWaitingPeriodModal}
				onClose={() => setShowWaitingPeriodModal(false)}
				treatmentPlanStartDate={treatmentPlanStartDate}
			/>

			{/* Extend TP Waiting Period Modal */}
			<ExtendTPWaitingPeriodModal
				open={showExtendTPWaitingPeriodModal}
				onClose={() => setShowExtendTPWaitingPeriodModal(false)}
				treatmentPlanEndDate={treatmentPlanEndDate}
			/>
		</ThemeProvider>
	);
}

export default Home;
