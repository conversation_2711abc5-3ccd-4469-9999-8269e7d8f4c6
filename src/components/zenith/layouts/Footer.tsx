import * as React from "react";
// import '../../../styles/zenith/main.css';
import Grid from "@mui/material/Grid2";
import { Divider, Stack, Typography } from "@mui/material";

function Footer() {
	return (
		<footer className="zenith-footer">
			<center>
				<Grid container sx={{ width: "100%", mt: 5, p: 2 }} direction={"column"} justifyContent={"center"}>
					<Grid
						size={12}
						container
						width={"100%"}
						justifyContent={"center"}
						alignItems={"center"}
						spacing={2}
					>
						<Stack maxWidth={500}>
							<Typography align="left" variant="caption" sx={{ color: "#86888A", mb: 2 }}>
								Any information you provide today is confidential and compliant with the Medical Board
								of Australia Good Medical Practice code, RACGP Standards of General Practice and our
								Medical Confidentiality Duty of Conduct for doctors in Australia, which means we protect
								your privacy and right to confidentiality
							</Typography>
						</Stack>
					</Grid>

					<Divider style={{ backgroundColor: "black", width: "100%" }} />
					<Typography sx={{ fontSize: "12px", mt: 2 }}>
						Provided by <span style={{ fontWeight: "600", color: "green" }}>ZenithClinics</span> Pty Ltd
					</Typography>
				</Grid>
			</center>
		</footer>
	);
}

export default Footer;
