import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Button,
  FormControl,
  FormControlLabel,
  RadioGroup,
  Radio,
  TextField,
  Checkbox,
  FormGroup,
  Slider,

  FormLabel,
  CircularProgress,
  Alert,
  <PERSON>ack,
  Grid2 as Grid,
} from "@mui/material";
import { useSnackbar } from "notistack";
import { QuestionnaireConfigResponse, QuestionConfig } from "../../../types";
import QuestionnaireService from "../../../services/questionnaireService";

interface DynamicQuestionnaireProps {
  questionnaireType: string;
  patientId?: string;
  onSubmit: (submissionData: any) => void;
  onScoreChange?: (score: number, maxScore: number, isEligible: boolean) => void;
  onStepChange?: (currentStep: number, totalSteps: number) => void;
}

const DynamicQuestionnaire: React.FC<DynamicQuestionnaireProps> = ({
  questionnaireType,
  patientId,
  onSubmit,
  onScoreChange,
  onStepChange,
}) => {
  const { enqueueSnackbar } = useSnackbar();
  
  // State
  const [config, setConfig] = useState<QuestionnaireConfigResponse | null>(null);
  const [steps, setSteps] = useState<any[]>([]);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  // Load questionnaire configuration
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setIsLoading(true);
        const questionnaireConfig = await QuestionnaireService.getQuestionnaireConfig(
          questionnaireType,
          patientId
        );
        setConfig(questionnaireConfig);

        // Convert sections to steps
        const stepConfigs = QuestionnaireService.groupQuestionsBySection(questionnaireConfig);
        setSteps(stepConfigs);

        // Initialize form data with default values
        const initialFormData: Record<string, any> = {};
        questionnaireConfig.questions.forEach(question => {
          if (question.type === 'checkbox') {
            initialFormData[question.key] = false;
          } else if (question.type === 'slider') {
            // Use the minimum value from slider config as default
            initialFormData[question.key] = question.sliderConfig?.min || 1;
          } else {
            initialFormData[question.key] = '';
          }
        });
        setFormData(initialFormData);
      } catch (error) {
        console.error('Failed to load questionnaire config:', error);
        enqueueSnackbar('Failed to load questionnaire', { 
          variant: 'error',
          autoHideDuration: 6000,
          preventDuplicate: true
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadConfig();
  }, [questionnaireType, patientId, enqueueSnackbar]);

  // Calculate score when form data changes
  useEffect(() => {
    if (config && config.questions && config.questions.length > 0 && Object.keys(formData).length > 0) {
      try {
        const { totalScore } = QuestionnaireService.calculateScore(formData, config);
        const isEligible = totalScore >= config.threshold;

        if (onScoreChange) {
          onScoreChange(totalScore, config.maxScore, isEligible);
        }
      } catch (error) {
        console.error('Error calculating score:', error);
        // Provide fallback values
        if (onScoreChange) {
          onScoreChange(0, config.maxScore, false);
        }
      }
    }
  }, [formData, config, onScoreChange]);

  // Notify parent of step changes
  useEffect(() => {
    if (steps.length > 0 && onStepChange) {
      onStepChange(currentStep, steps.length);
    }
  }, [currentStep, steps.length, onStepChange]);

  // Handle form field changes
  const handleFieldChange = (questionKey: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [questionKey]: value
    }));
    
    // Clear errors when user makes changes
    if (errors.length > 0) {
      setErrors([]);
    }
  };

  // Validate current step
  const validateCurrentStep = (): boolean => {
    if (!config || steps.length === 0) return false;

    const currentStepConfig = steps.find(step => step.stepNumber === currentStep);
    if (!currentStepConfig) return false;

    const stepErrors: string[] = [];

    // Group questions by type for validation
    const checkboxQuestions = currentStepConfig.questions.filter((q: QuestionConfig) => q.type === 'checkbox');
    const otherQuestions = currentStepConfig.questions.filter((q: QuestionConfig) => q.type !== 'checkbox');

    // For checkbox questions, validate that at least one is selected
    if (checkboxQuestions.length > 0) {
      const hasAnyCheckboxSelected = checkboxQuestions.some((question: QuestionConfig) => {
        const value = formData[question.key];
        return value === true || value === 'true';
      });

      if (!hasAnyCheckboxSelected) {
        stepErrors.push('Please select at least one option');
      }
    }

    // For other question types, validate individually
    otherQuestions.forEach((question: QuestionConfig) => {
      if (QuestionnaireService.shouldShowQuestion(question, formData)) {
        const value = formData[question.key];

        // Check if this is a conditional text field that should be shown
        let shouldShow = true;

        if (question.type === 'text') {
          // Handle "Other reason" text field
          if (question.key === 'reasonOtherText' ||
              (question.key.toLowerCase().includes('other') &&
               (question.text.toLowerCase().includes('describe') || question.text.toLowerCase().includes('other')))) {
            // Check if "Other reason" checkbox is selected (from any step, not just current step)
            const otherCheckboxSelected = formData['reasonOther'] === true || formData['reasonOther'] === 'true';
            shouldShow = otherCheckboxSelected;
          }

          // Handle "Side effects description" text field
          if (question.key === 'sideEffectsDescription' ||
              (question.text.toLowerCase().includes('description') &&
               question.text.toLowerCase().includes('side effects'))) {
            const hasModerateOrStrong = formData['sideEffectsModerate'] || formData['sideEffectsStrong'];
            shouldShow = hasModerateOrStrong;
          }

          // Handle "Health changes description" text field
          if (question.key === 'healthChangesDescription' ||
              (question.key.toLowerCase().includes('healthchanges') && question.key.toLowerCase().includes('description')) ||
              (question.text.toLowerCase().includes('description') &&
               question.text.toLowerCase().includes('health changes'))) {
            const healthChangesYes = formData['healthChanges'] === 'yes_please_describe' ||
                                   formData['healthChanges'] === 'yes' ||
                                   formData['healthChanges'] === 'Yes';
            shouldShow = healthChangesYes;
          }
        }

        // Only validate if the field should be shown AND is required according to backend
        let isRequired = true; // Default to required

        // For text fields, check textFieldConfig.required
        if (question.type === 'text' && question.textFieldConfig) {
          isRequired = question.textFieldConfig.required !== false;
        }

        // For radio fields, they are typically required unless specified otherwise
        // For other field types, use default required behavior

        if (shouldShow && isRequired && (value === undefined || value === null || value === '')) {
          stepErrors.push(`${question.text} is required`);
        }
      }
    });

    setErrors(stepErrors);
    return stepErrors.length === 0;
  };

  // Handle next step
  const handleNext = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => prev + 1);
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
    setErrors([]);
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Prevent double submissions
    if (isSubmitting) return;
    
    if (!config) return;

    // Set loading state immediately to show spinner and prevent further clicks
    setIsSubmitting(true);
    setErrors([]);

    try {
      const validation = QuestionnaireService.validateFormData(formData, config);
      if (!validation.isValid) {
        setErrors(validation.errors);
        enqueueSnackbar('Please complete all required fields', { 
          variant: 'error',
          autoHideDuration: 6000,
          preventDuplicate: true
        });
        return;
      }

      const submissionData = QuestionnaireService.formatSubmissionData(
        formData,
        config,
        patientId || ''
      );
      
      // Await the onSubmit call to keep loading state until parent API call completes
      await onSubmit(submissionData);
    } catch (error) {
      console.error('Failed to submit questionnaire:', error);
      enqueueSnackbar('Failed to submit questionnaire', { 
        variant: 'error',
        autoHideDuration: 6000,
        preventDuplicate: true
      });
    } finally {
      // Always reset loading state, regardless of success or failure
      setIsSubmitting(false);
    }
  };

  // Render step content exactly like the original Add22Thc component
  const renderStepContent = () => {
    if (!config || steps.length === 0) return null;

    const currentStepConfig = steps.find(step => step.stepNumber === currentStep);
    if (!currentStepConfig) return null;

    // Sort questions by their order property to maintain proper sequence
    const sortedQuestions = [...currentStepConfig.questions].sort((a, b) => a.order - b.order);

    // Group consecutive checkbox questions to render them together
    const questionGroups: Array<{ type: 'checkbox-group' | 'single', questions: QuestionConfig[] }> = [];
    let currentGroup: QuestionConfig[] = [];
    let currentGroupType: 'checkbox' | 'other' | null = null;

    sortedQuestions.forEach((question, index) => {
      if (question.type === 'checkbox') {
        if (currentGroupType !== 'checkbox') {
          // Start a new checkbox group
          if (currentGroup.length > 0) {
            questionGroups.push({ type: 'single', questions: [...currentGroup] });
          }
          currentGroup = [question];
          currentGroupType = 'checkbox';
        } else {
          // Continue current checkbox group
          currentGroup.push(question);
        }
      } else {
        if (currentGroupType === 'checkbox') {
          // End checkbox group and start new single question group
          questionGroups.push({ type: 'checkbox-group', questions: [...currentGroup] });
          currentGroup = [question];
          currentGroupType = 'other';
        } else {
          // Continue or start single question group
          if (currentGroup.length > 0) {
            questionGroups.push({ type: 'single', questions: [...currentGroup] });
          }
          currentGroup = [question];
          currentGroupType = 'other';
        }
      }

      // Handle last question
      if (index === sortedQuestions.length - 1) {
        if (currentGroupType === 'checkbox') {
          questionGroups.push({ type: 'checkbox-group', questions: [...currentGroup] });
        } else {
          questionGroups.push({ type: 'single', questions: [...currentGroup] });
        }
      }
    });

    return (
      <Stack sx={{ 
        flexGrow: 1, 
        width: "100%", 
        alignItems: "flex-start",
        textAlign: "left !important",
        "& *": {
          textAlign: "left !important"
        }
      }} className="questionnaire-container">
        {questionGroups.map((group, groupIndex) => {
          if (group.type === 'checkbox-group') {
            // Render checkbox group
            return (
              <FormControl key={`checkbox-group-${groupIndex}`} sx={{ 
                width: "100%",
                textAlign: "left !important",
                "& .MuiFormLabel-root": {
                  textAlign: "left !important"
                },
                "& .MuiTypography-root": {
                  textAlign: "left !important"
                }
              }}>
                <FormLabel sx={{ 
                  color: "black", 
                  fontWeight: "bold",
                  textAlign: "left !important",
                  width: "100%",
                  display: "block"
                }}>
                  {currentStepConfig.title}
                </FormLabel>
                {currentStepConfig.section?.description && (
                  <Typography sx={{ 
                    mt: 1, 
                    mb: 1, 
                    color: 'text.secondary',
                    textAlign: "left !important",
                    width: "100%"
                  }}>
                    {currentStepConfig.section.description}
                  </Typography>
                )}
                <FormGroup sx={{ 
                  mt: 2,
                  textAlign: "left !important",
                  width: "100%"
                }}>
                  {group.questions.map((question: QuestionConfig) => {
                    const value = formData[question.key];
                    return (
                      <FormControlLabel
                        key={question.key}
                        control={
                          <Checkbox
                            checked={value === true || value === 'true'}
                            onChange={(e) => handleFieldChange(question.key, e.target.checked)}
                          />
                        }
                        label={question.text}
                        sx={{
                          alignItems: 'flex-start',
                          marginBottom: 1,
                          textAlign: "left !important",
                          width: "100%",
                          '.MuiFormControlLabel-label': {
                            mt: 0.5,
                            textAlign: 'left !important',
                            display: 'block',
                            width: "100%"
                          }
                        }}
                      />
                    );
                  })}
                </FormGroup>
              </FormControl>
            );
          } else {
            // Render single questions
            return group.questions.map((question: QuestionConfig) => {
          if (!QuestionnaireService.shouldShowQuestion(question, formData)) {
            return null;
          }

          // Special handling for conditional text fields
          if (question.type === 'text') {
            // Handle "Other reason" text field - only show if "Other" checkbox is checked
            if (question.key === 'reasonOtherText' ||
                (question.key.toLowerCase().includes('other') &&
                 (question.text.toLowerCase().includes('describe') || question.text.toLowerCase().includes('other')))) {
              // Check if "Other reason" checkbox is selected (from any step, not just current step)
              const otherCheckboxSelected = formData['reasonOther'] === true || formData['reasonOther'] === 'true';

              // Only show this text field if the "Other" checkbox is checked
              if (!otherCheckboxSelected) {
                return null;
              }
            }

            // Handle "Side effects description" text field - only show if moderate or strong side effects are checked
            if (question.key === 'sideEffectsDescription' ||
                (question.text.toLowerCase().includes('description') &&
                 question.text.toLowerCase().includes('side effects'))) {
              // Check if moderate or strong side effects are selected
              const hasModerateOrStrong = formData['sideEffectsModerate'] || formData['sideEffectsStrong'];

              // Only show this text field if moderate or strong side effects are checked
              if (!hasModerateOrStrong) {
                return null;
              }
            }

            // Handle "Health changes description" text field - only show if "Yes" is selected for health changes
            if (question.key === 'healthChangesDescription' ||
                (question.key.toLowerCase().includes('healthchanges') && question.key.toLowerCase().includes('description')) ||
                (question.text.toLowerCase().includes('description') &&
                 question.text.toLowerCase().includes('health changes'))) {
              // Check if "Yes" is selected for health changes (the actual value from backend is 'yes_please_describe')
              const healthChangesYes = formData['healthChanges'] === 'yes_please_describe' ||
                                     formData['healthChanges'] === 'yes' ||
                                     formData['healthChanges'] === 'Yes';

              // Only show this text field if "Yes" is selected
              if (!healthChangesYes) {
                return null;
              }
            }
          }

          const value = formData[question.key];

          switch (question.type) {

            case 'slider':
              const sliderMin = question.sliderConfig?.min || 1;
              const sliderMax = question.sliderConfig?.max || 10;
              const defaultValue = Number(value) || sliderMin;

              return (
                <FormControl key={question.key} sx={{ 
                  width: "100%",
                  textAlign: "left !important"
                }}>
                  <FormLabel sx={{ 
                    color: "black", 
                    fontWeight: "bold",
                    textAlign: "left !important",
                    width: "100%",
                    display: "block"
                  }}>
                    {question.text}
                  </FormLabel>
                  <Box sx={{ 
                    padding: "0 20px", 
                    marginBottom: "20px", 
                    mt: 2,
                    textAlign: "left !important"
                  }}>
                    <Slider
                      value={defaultValue}
                      onChange={(_, newValue) => handleFieldChange(question.key, newValue)}
                      min={sliderMin}
                      max={sliderMax}
                      step={1}
                      marks
                      valueLabelDisplay="on"
                    />
                  </Box>
                </FormControl>
              );

            case 'radio':
              return (
                <FormControl key={question.key} sx={{ 
                  width: "100%",
                  textAlign: "left !important"
                }}>
                  <FormLabel sx={{ 
                    color: "black", 
                    fontWeight: "bold",
                    textAlign: "left !important",
                    width: "100%",
                    display: "block"
                  }}>
                    {question.text}
                  </FormLabel>
                  <RadioGroup
                    value={value || ''}
                    onChange={(e) => handleFieldChange(question.key, e.target.value)}
                    sx={{ 
                      mt: 2,
                      textAlign: "left !important"
                    }}
                  >
                    {question.answerOptions?.map((option) => (
                      <FormControlLabel
                        key={option.value}
                        value={option.value}
                        control={<Radio />}
                        label={option.label}
                        sx={{
                          alignItems: 'flex-start',
                          marginBottom: 1,
                          textAlign: "left !important",
                          width: "100%",
                          '.MuiFormControlLabel-label': {
                            mt: 0.5,
                            textAlign: 'left !important',
                            display: "block",
                            width: "100%"
                          }
                        }}
                      />
                    ))}
                  </RadioGroup>
                </FormControl>
              );

            case 'text':
              return (
                <FormControl key={question.key} sx={{ 
                  mt: 2, 
                  width: "100%",
                  textAlign: "left !important"
                }}>
                  <FormLabel sx={{ 
                    color: "black", 
                    fontWeight: "bold",
                    textAlign: "left !important",
                    width: "100%",
                    display: "block"
                  }}>
                    {question.text}
                  </FormLabel>
                  <TextField
                    value={value || ''}
                    onChange={(e) => handleFieldChange(question.key, e.target.value)}
                    multiline
                    rows={4}
                    placeholder="Please provide details..."
                    sx={{ 
                      mt: 1,
                      textAlign: "left !important",
                      "& .MuiInputBase-input": {
                        textAlign: "left !important"
                      }
                    }}
                  />
                </FormControl>
              );

            default:
              return null;
          }
            });
          }
        })}
      </Stack>
    );
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (!config) {
    return (
      <Alert severity="error">
        Failed to load questionnaire configuration
      </Alert>
    );
  }

  const isLastStep = currentStep === steps.length;

  return (
    <>
      <Grid size={12} sx={{ width: "100%" }} className="questionnaire-container">{renderStepContent()}</Grid>
      <Grid size={12} sx={{ 
        width: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
      }}>
        <Button
          sx={{
            textTransform: "capitalize",
            borderRadius: "20px",
            opacity: isSubmitting ? 0.7 : 1,
            minHeight: "48px", // Ensure consistent height for spinner
            position: "relative", // For proper spinner positioning
            width: { xs: "100%", sm: "auto" }, // Full width on mobile, auto on desktop
            maxWidth: "400px" // Limit maximum width on larger screens
          }}
          variant="contained"
          color="success"
          disableElevation
          size="large"
          onClick={isLastStep ? handleSubmit : handleNext}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <CircularProgress 
                size={20} 
                color="inherit" 
                sx={{ 
                  marginRight: 1,
                  color: "white" // Ensure spinner is visible on green background
                }} 
              />
              Submitting...
            </>
          ) : isLastStep ? (
            "Submit"
          ) : (
            "Continue"
          )}
        </Button>
        {errors.length > 0 && (
          <Typography
            variant="body2"
            color="error"
            sx={{ 
              mt: 1, 
              textAlign: 'center',
              width: "100%"
            }}
          >
            {errors[0]}
          </Typography>
        )}
      </Grid>
      {/* {currentStep > 1 && (
        <Grid size={3}>
          <Button
            fullWidth
            sx={{
              textTransform: "capitalize",
              borderRadius: "20px",
            }}
            variant="outlined"
            color="success"
            disableElevation
            size="large"
            onClick={handlePrevious}
            disabled={isSubmitting}
          >
            Back
          </Button>
        </Grid>
      )} */}
    </>
  );
};

export default DynamicQuestionnaire;
