// hooks/useCaptureUTM.ts
import { useEffect } from 'react';
import { useLocation } from '@tanstack/react-location';

type UTMParams = {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  fbclid?: string;
  timestamp?: number;
};

const UTM_KEYS: (keyof UTMParams)[] = [
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_term',
  'utm_content',
  'fbclid'
];

// Set TTL in milliseconds (e.g., 3 days)
const TTL = 3 * 24 * 60 * 60 * 1000;

export const useCaptureUTM = (): void => {
  const location = useLocation();

  useEffect(() => {
    const search = location.current.searchStr;
    const searchParams = new URLSearchParams(search);

    // Get and validate existing UTM from storage
    const storedRaw = localStorage.getItem('utm_params');
    let stored: UTMParams = {};

    if (storedRaw) {
      try {
        stored = JSON.parse(storedRaw);
        const isExpired =
          stored.timestamp && Date.now() - stored.timestamp > TTL;
        if (isExpired) {
          localStorage.removeItem('utm_params');
          stored = {};
        }
      } catch {
        localStorage.removeItem('utm_params');
      }
    }

    // Extract new UTM params from URL
    let utmParams: UTMParams = {};
    UTM_KEYS.forEach((key) => {
      const value = searchParams.get(key);
      if (value && key != 'timestamp') {
        utmParams[key] = value;
      }
    });

    // Save only if new UTM exists and no valid (non-expired) data is stored
    if (Object.keys(utmParams).length > 0 && Object.keys(stored).length === 0) {
      utmParams.timestamp = Date.now();
      localStorage.setItem('utm_params', JSON.stringify(utmParams));
    }
  }, [location.current.searchStr]);
};
