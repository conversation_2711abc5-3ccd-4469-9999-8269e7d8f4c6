import { createContext, JS<PERSON>, useContext, useEffect, useState, useCallback } from "react";
import LoadingScreen from "../utils/loading-screen";
import axios from "axios";
import { useLocation, useNavigate } from "@tanstack/react-location";
import { AuthUser } from "../types";
import axiosInstance from "../services/axios";

type FlowContextValues = {
	authenticated: boolean;
	setUser: (user: AuthUser | null) => void;
	user: AuthUser | null;
	loginWithLeadId: (leadId: string) => Promise<void>;
	flowController: (user: AuthUser) => void;
};

interface FlowProviderProps {
	children: React.ReactNode;
}

const FlowContext = createContext<FlowContextValues | null>(null);

export const FlowController = ({ children }: FlowProviderProps): JSX.Element => {
	// Initialize state from localStorage if available
	const initialUser = localStorage.getItem("zenith_auth_user")
		? (JSON.parse(localStorage.getItem("zenith_auth_user") || "{}") as AuthUser)
		: null;

	const initialAuth = localStorage.getItem("zenith_authenticated") === "true";
	const [authenticated, setAuthenticated] = useState(initialAuth);
	const [isLoading, setIsLoading] = useState(true);
	const [user, setUser] = useState<AuthUser | null>(initialUser);
	const [initialized, setInitialized] = useState(false);
	const navigate = useNavigate();
	const location = useLocation();

	const paths = {
		registration: "/patient/register",
		questionnaire: "/patient/questionnaire",
		discharge: "/patient/discharge-letter",
	};

	const strictPages = [
		"/patient/questionnaire",
		"/patient/phone-verification",
		"/patient/discharge-letter",
		"/patient/schedule",
		"/patient/profile",
		"/patient/chat",
		"/patient/home",
		"/patient/bookings",
		"/patient/increase",
		"/patient/extend",
		"/patient/add-22-thc",
		"/patient/add-22-thc-dynamic",
		"/patient/quantity-increase",
		"/schedule",
	];

	const noLogin = [
		"/patient/consent",
		"/patient/review",
		"/patient/login",
		"/patient/confirm",
		"/patient/reset-password",
		"/patient/terms-conditions",
		"/patient/forgot-password",
		"/patient/forgot-phone-verification",
		"/patient/introduce-harvest",
		"/patient/confirm-on-call-booking",
		"/patient/register",
		"/patient/health-survey",
		"/harvest",
	];

	// Helper function to check if URL has contactId (profile or home)
	const isUrlWithContactId = (pathname: string): boolean => {
		const profilePattern = /^\/patient\/profile\/[^/]+$/;
		const homePattern = /^\/patient\/home\/[^/]+$/;
		return profilePattern.test(pathname) || homePattern.test(pathname);
	};

	// Helper function to extract contactId from URL (profile or home)
	const extractContactIdFromUrl = (pathname: string): string | null => {
		const profileMatch = pathname.match(/^\/patient\/profile\/([^/]+)$/);
		const homeMatch = pathname.match(/^\/patient\/home\/([^/]+)$/);
		return profileMatch ? profileMatch[1] : homeMatch ? homeMatch[1] : null;
	};

	const steps = [
		"registration", // does this include phone number verification
		"questionnaire",
		"discharge",
		"booking",
	];

	const redirectToSchedule = () => {
		axios
			.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/encrypt-id`, { user }, { withCredentials: true })
			.then((response) => {
				const encryptedLeadID = response.data.data.leadID;
				const baseUrl = `${import.meta.env.VITE_DRUI_URL}/schedule`;
				const url = `${baseUrl}?token=${encryptedLeadID}`;
				window.location.href = url;
			});
	};

	const setUserWithStorage = useCallback((newUser: AuthUser | null) => {
		// Update React state
		const userLoggedIn = newUser as Partial<AuthUser>;

		setUser(newUser);
		setAuthenticated(!!newUser);

		// Update localStorage for consent page
		if (newUser) {
			delete userLoggedIn.password;
			localStorage.setItem("zenith_auth_user", JSON.stringify(userLoggedIn));
			localStorage.setItem("zenith_authenticated", "true");
		} else {
			localStorage.removeItem("zenith_auth_user");
			localStorage.removeItem("zenith_authenticated");
		}
	}, []);

	const loginWithLeadId = useCallback(
		async (leadID: string) => {
			try {
				const result = await axios.post(
					`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/login`,
					{ leadID },
					{ withCredentials: true }
				);

				if (result.data?.authenticated) {
					const userLoggedIn = result.data?.user as Partial<AuthUser>;
					// Update state
					setAuthenticated(true);
					setUser(result.data?.user as AuthUser);

					// remote password - Do not expose password
					delete userLoggedIn?.password;

					// Also store in localStorage for consent page
					localStorage.setItem("zenith_auth_user", JSON.stringify(userLoggedIn));
					localStorage.setItem("zenith_authenticated", "true");

					// Navigate to phone verification
					navigate({ to: "/patient/phone-verification" });
				} else {
					setAuthenticated(false);
					setUser(null);

					// Clear localStorage
					localStorage.removeItem("zenith_auth_user");
					localStorage.removeItem("zenith_authenticated");
				}
			} catch (error) {
				setAuthenticated(false);
				setUser(null);

				// Clear localStorage on error
				localStorage.removeItem("zenith_auth_user");
				localStorage.removeItem("zenith_authenticated");
			}
		},
		[navigate]
	);

	const flowController = (user: AuthUser) => {
		const userLoggedIn = user as Partial<AuthUser>;
		const verified = user?.phoneverified;
		const lastStep = user.laststep;
		const totalSteps = steps.length;
		const lastStepIndex = steps.indexOf(lastStep);
		const nextStep = lastStepIndex + 1;

		const initialIntentPath = localStorage.getItem("initialPath");
		const foundStrict = strictPages.find((e) => e === initialIntentPath);

		const nextStepValue = steps[nextStep];
		const nextPath = nextStepValue && paths[nextStepValue as keyof typeof paths];
		const currentPath = location.current.pathname;
		// avoid exposting password
		delete userLoggedIn.password;
		localStorage.setItem("zenith_auth_user", JSON.stringify(userLoggedIn));
		localStorage.setItem("zenith_authenticated", "true");

		if (user.status === "rejected") {
			navigate({ to: "/patient/not-approved" });
			return;
		}

		// Special handling for discharge form
		if (initialIntentPath === "/patient/discharge-letter") {
			navigate({ to: initialIntentPath });
			return;
		}

		// Special handling for schedule routes
		if (initialIntentPath === "/patient/schedule" || initialIntentPath === "/schedule") {
			navigate({ to: initialIntentPath });
			return;
		}

		// Special handling for users who have completed consent form
		if (lastStep === "consent") {
			navigate({ to: "/patient/home" });
			return;
		}

		if (!foundStrict && initialIntentPath == "/patient/consent") {
			navigate({ to: initialIntentPath });
		} else if (!verified) {
			navigate({ to: "/patient/phone-verification" });
		} else if (lastStep === "booking") {
			// Users who have completed booking should go to home page
			navigate({ to: "/patient/home" });
		} else if (nextStep === totalSteps) {
			// Check if user has treatment plan before redirecting to shop
			axiosInstance
				.get(`/zoho/v1.0/treatment-plan/by-email?email=${encodeURIComponent(user.email)}`)
				.then((response) =>
					response.data?.success && response.data?.treatmentPlan
						? (window.location.href = import.meta.env.VITE_LETSROLL_URL)
						: navigate({ to: "/patient/home" })
				)
				.catch(() => navigate({ to: "/patient/home" }));
		} else if (nextStep < totalSteps) {
			// Check if the status of the user
			if (user.status === "pending") {
				if (user?.uploaded_documents?.length > 0) {
					const hasMissingFile = user.uploaded_documents.some((doc) => !doc.file_url);
					if (
						hasMissingFile &&
						currentPath !== "/patient/upload-blood-pressure" &&
						currentPath !== "/patient/upload-heart-rate"
					) {
						navigate({ to: "/patient/pending-review" });
						return;
					}
				}
				navigate({ to: "/patient/pending-review" });
			} else if (user.status === "rejected") {
				navigate({ to: "/patient/not-approved" });
			} else {
				if (nextStepValue === "booking") {
					redirectToSchedule();
				} else {
					navigate({ to: nextPath });
				}
			}
		}
	};

	useEffect(() => {
		const init = async () => {
			// Prevent multiple initializations
			if (initialized) return;

			try {
				localStorage.setItem("initialPath", location.current.pathname);
				const allowed = noLogin.find((e) => location.current.pathname.startsWith(e));
				const isUrlWithContactIdPath = isUrlWithContactId(location.current.pathname);

				if (allowed) {
					setIsLoading(false);
					setInitialized(true);
					return;
				} else if (isUrlWithContactIdPath) {
					// Handle contactId authentication in flow-controller
					const contactId = extractContactIdFromUrl(location.current.pathname);
					if (contactId) {
						console.log("Flow-controller: Attempting contactId authentication");
						try {
							// Import and use the contactId auth function
							const { authenticateByContactId } = await import("../utils/contactId-auth");
							const authResult = await authenticateByContactId(contactId);

							if (authResult.success && authResult.user) {
								setAuthenticated(true);
								setUser(authResult.user as AuthUser);
								console.log("Flow-controller: ContactId authentication successful");
							} else {
								console.log("Flow-controller: ContactId authentication failed");
								navigate({ to: "/patient/login" });
							}
						} catch (error) {
							console.error("Flow-controller: ContactId authentication error:", error);
							navigate({ to: "/patient/login" });
						}
					}
					setIsLoading(false);
					setInitialized(true);
					return;
				} else {
					const result = await axiosInstance.get(
						`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/validate`,
						{
							withCredentials: true,
						}
					);

					if (!result.data?.authenticated) {
						navigate({ to: "/patient/login" });
						setAuthenticated(false);
					} else {
						setAuthenticated(true);
						setUser(result.data?.user as AuthUser);

						// Don't run flow controller for profile, chat, and landing pages to prevent loops
						const currentPath = location.current.pathname;
						if (
							currentPath !== "/patient/profile" &&
							currentPath !== "/patient/chat" &&
							currentPath !== "/patient/home" &&
							currentPath !== "/patient/upload-blood-pressure" &&
							currentPath !== "/patient/upload-heart-rate"
						) {
							flowController(result.data?.user as AuthUser);
						}
						setIsLoading(false);
					}
					setInitialized(true);
				}
			} catch (e) {
				console.log("An error happened here");
				navigate({ to: "/patient/login" });
				setIsLoading(false);
				setInitialized(true);
			}
		};
		init();
	}, [initialized]);

	return (
		<FlowContext.Provider
			value={{
				authenticated,
				loginWithLeadId,
				flowController,
				user,
				setUser: setUserWithStorage,
			}}
		>
			{isLoading ? <LoadingScreen /> : children}
		</FlowContext.Provider>
	);
};

export const useFlow = () => {
	const context = useContext(FlowContext);

	if (!context) {
		throw new Error("useAuth must be used within an AuthProvider");
	}
	return context;
};
